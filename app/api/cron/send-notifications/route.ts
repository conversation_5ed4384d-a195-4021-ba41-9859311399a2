/**
 * API Route sécurisée pour l'envoi de notifications (Cron Job)
 * Epic 6 - Notifications Utilisateur
 *
 * Cette route est appelée périodiquement par Vercel Cron pour :
 * 1. Récupérer tous les utilisateurs avec des préférences de notification actives
 * 2. Vérifier s'ils ont de nouvelles recommandations
 * 3. Envoyer des emails et/ou notifications push selon leurs préférences
 * 4. Respecter la fréquence choisie (weekly, biweekly, monthly)
 */

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, recommendations, userFCMTokens } from "@/lib/db/schema";
import { eq, and, gte, ne, desc } from "drizzle-orm";
import {
  createEmailSchedule,
  isGoodTimeToSend,
  hasRecentEmail,
  updateLastEmailSent
} from "@/lib/email-timing";
// Note: Le throttling avancé a été simplifié pour cette version

/**
 * POST - Cron job d'envoi des notifications
 */
export async function POST(request: NextRequest) {
  if (process.env.NODE_ENV === 'development') {
  console.log("📧 Démarrage du job d'envoi de notifications");
  }

  try {
    // Vérification de sécurité : authentification par secret
    const authHeader = request.headers.get("authorization");
    const expectedSecret = `Bearer ${process.env.CRON_SECRET}`;

    if (!authHeader || authHeader !== expectedSecret) {
      console.error("❌ Tentative d'accès non autorisée au cron job de notifications");
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Calculer les dates pour les différentes fréquences
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Note: Vérification d'horaire supprimée - le cron Vercel contrôle déjà l'heure d'exécution

    // ÉTAPE 1: Récupérer les utilisateurs éligibles pour les notifications email
    if (process.env.NODE_ENV === 'development') {
    console.log("📧 Phase 1: Traitement des notifications email");
    }
    const emailResults = await processEmailNotifications(oneWeekAgo, twoWeeksAgo, oneMonthAgo);

    // ÉTAPE 2: Récupérer les utilisateurs éligibles pour les notifications push
    if (process.env.NODE_ENV === 'development') {
    console.log("📱 Phase 2: Traitement des notifications push");
    }
    const pushResults = await processPushNotifications(oneWeekAgo, twoWeeksAgo, oneMonthAgo);

    if (process.env.NODE_ENV === 'development') {
    console.log(`🎯 Job de notifications terminé:`);
    }
    console.log(`   Emails: ${emailResults.successCount} succès, ${emailResults.errorCount} erreurs`);
    if (process.env.NODE_ENV === 'development') {
    console.log(`   Push: ${pushResults.successCount} succès, ${pushResults.errorCount} erreurs`);
    }

    return NextResponse.json({
      success: true,
      message: "Job de notifications terminé",
      email: emailResults,
      push: pushResults,
      totalProcessed: emailResults.processedUsers + pushResults.processedUsers
    });

  } catch (error) {
    console.error("💥 Erreur critique dans le job de notifications:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Erreur interne du serveur",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * Traite les notifications email selon les préférences des utilisateurs
 */
async function processEmailNotifications(oneWeekAgo: Date, twoWeeksAgo: Date, oneMonthAgo: Date) {
  let processedUsers = 0;
  let successCount = 0;
  let errorCount = 0;

  try {
    // Récupérer les utilisateurs avec notifications email activées
    // ET qui ont déjà reçu leur premier email de recommandations
    const usersForEmail = await db.query.users.findMany({
      where: and(
        ne(users.emailFrequency, 'never'),
        eq(users.firstRecommendationEmailSent, true)
      ),
      columns: {
        id: true,
        name: true,
        email: true,
        emailFrequency: true,
        preferredLanguage: true
      }
    });

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 ${usersForEmail.length} utilisateurs avec notifications email activées`);
    }

    if (usersForEmail.length === 0) {
      return { processedUsers: 0, successCount: 0, errorCount: 0, results: [] };
    }

    // Note: Le throttling avancé a été simplifié pour cette version

    // Traiter chaque utilisateur avec throttling
    const results = await Promise.allSettled(
      usersForEmail.map(async (user) => {
        try {
          processedUsers++;

          // Vérifier si l'utilisateur a reçu un email récemment
          const hasRecentEmailSent = await hasRecentEmail(user.id, 24);
          if (hasRecentEmailSent) {
            return { userId: user.id, status: "skipped", reason: "Recent email sent" };
          }

          // Note: Le throttling avancé a été simplifié pour cette version

          // Déterminer la date limite selon la fréquence
          let sinceDate: Date;
          switch (user.emailFrequency) {
            case 'weekly':
              sinceDate = oneWeekAgo;
              break;
            case 'bi-weekly':
              sinceDate = twoWeeksAgo;
              break;
            case 'monthly':
              sinceDate = oneMonthAgo;
              break;
            default:
              return { userId: user.id, status: "skipped", reason: "Invalid frequency" };
          }

          // Récupérer les nouvelles recommandations pour l'email (uniquement shortterm)
          // Note: Le filtrage avancé (doublons, Discogs, liens d'achat) sera fait dans sendRecommendationsEmail
          const newRecommendations = await db.query.recommendations.findMany({
            where: and(
              eq(recommendations.userId, user.id),
              eq(recommendations.timeframe, 'short_term'), // Uniquement les recommandations shortterm
              gte(recommendations.generatedAt, sinceDate)
            ),
            columns: {
              artistName: true,
              albumTitle: true,
              albumCoverUrl: true,
              listenScore: true,
              affiliateLinks: true,
              isOwned: true
            },
            orderBy: [desc(recommendations.listenScore)],
            limit: 50 // Récupérer plus de recommandations pour avoir un bon pool après filtrage
          });

          if (newRecommendations.length === 0) {
            return { userId: user.id, status: "no_recommendations" };
          }

          // Envoyer l'email
          const { sendRecommendationsEmail } = await import('@/lib/resend');
          const emailUserData = {
            id: user.id,
            name: user.name || undefined,
            email: user.email!,
            preferredLanguage: user.preferredLanguage
          };

          // Mapper les recommandations pour convertir null en undefined et typer correctement
          const emailRecommendations = newRecommendations.map(rec => ({
            artistName: rec.artistName,
            albumTitle: rec.albumTitle,
            albumCoverUrl: rec.albumCoverUrl || undefined,
            listenScore: rec.listenScore,
            affiliateLinks: Array.isArray(rec.affiliateLinks) ? rec.affiliateLinks : undefined,
            isOwned: rec.isOwned
          }));

          const emailResult = await sendRecommendationsEmail(emailUserData, emailRecommendations);

          if (emailResult.success) {
            // Note: Le throttling avancé a été simplifié pour cette version

            // Mettre à jour la date du dernier email envoyé
            await updateLastEmailSent(user.id);

            successCount++;
            return {
              userId: user.id,
              status: "success",
              messageId: emailResult.messageId,
              recommendationsCount: newRecommendations.length
            };
          } else {
            errorCount++;
            return { 
              userId: user.id, 
              status: "error", 
              error: emailResult.error 
            };
          }

        } catch (error) {
          errorCount++;
          console.error(`❌ Erreur lors du traitement email pour l'utilisateur ${user.id}:`, error);
          return {
            userId: user.id,
            status: "error",
            error: error instanceof Error ? error.message : "Unknown error"
          };
        }
      })
    );

    return {
      processedUsers,
      successCount,
      errorCount,
      results: results.map(r => r.status === "fulfilled" ? r.value : { status: "error" })
    };

  } catch (error) {
    console.error("❌ Erreur critique lors du traitement des emails:", error);
    return { processedUsers, successCount, errorCount, results: [] };
  }
}

/**
 * Traite les notifications push selon les préférences des utilisateurs
 */
async function processPushNotifications(oneWeekAgo: Date, twoWeeksAgo: Date, oneMonthAgo: Date) {
  let processedUsers = 0;
  let successCount = 0;
  let errorCount = 0;

  try {
    // Récupérer les utilisateurs avec notifications push activées
    const usersForPush = await db.query.users.findMany({
      where: ne(users.pushFrequency, 'never'),
      columns: {
        id: true,
        name: true,
        pushFrequency: true,
        preferredLanguage: true
      }
    });

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 ${usersForPush.length} utilisateurs avec notifications push activées`);
    }

    if (usersForPush.length === 0) {
      return { processedUsers: 0, successCount: 0, errorCount: 0, results: [] };
    }

    // Traiter chaque utilisateur
    const results = await Promise.allSettled(
      usersForPush.map(async (user) => {
        try {
          processedUsers++;

          // Déterminer la date limite selon la fréquence
          let sinceDate: Date;
          switch (user.pushFrequency) {
            case 'weekly':
              sinceDate = oneWeekAgo;
              break;
            case 'bi-weekly':
              sinceDate = twoWeeksAgo;
              break;
            case 'monthly':
              sinceDate = oneMonthAgo;
              break;
            default:
              return { userId: user.id, status: "skipped", reason: "Invalid frequency" };
          }

          // Récupérer les nouvelles recommandations non possédées
          const newRecommendations = await db.query.recommendations.findMany({
            where: and(
              eq(recommendations.userId, user.id),
              gte(recommendations.generatedAt, sinceDate),
              eq(recommendations.isOwned, false)
            ),
            limit: 1 // Juste pour compter
          });

          if (newRecommendations.length === 0) {
            return { userId: user.id, status: "no_recommendations" };
          }

          // Récupérer les tokens FCM actifs de l'utilisateur
          const fcmTokens = await db.query.userFCMTokens.findMany({
            where: and(
              eq(userFCMTokens.userId, user.id),
              eq(userFCMTokens.isActive, true)
            ),
            columns: {
              token: true
            }
          });

          if (fcmTokens.length === 0) {
            return { userId: user.id, status: "no_tokens" };
          }

          // Générer et envoyer la notification push
          const { generatePushNotificationContent, sendMulticastPushNotification } = await import('@/lib/firebase-messaging');
          
          const notificationContent = generatePushNotificationContent(
            user.preferredLanguage,
            newRecommendations.length,
            user.name || undefined
          );

          const pushResult = await sendMulticastPushNotification(
            fcmTokens.map(t => t.token),
            notificationContent
          );

          if (pushResult.success) {
            successCount++;
            return { 
              userId: user.id, 
              status: "success", 
              tokensCount: fcmTokens.length,
              successCount: pushResult.successCount,
              recommendationsCount: newRecommendations.length
            };
          } else {
            errorCount++;
            return { 
              userId: user.id, 
              status: "error", 
              tokensCount: fcmTokens.length,
              failureCount: pushResult.failureCount
            };
          }

        } catch (error) {
          errorCount++;
          console.error(`❌ Erreur lors du traitement push pour l'utilisateur ${user.id}:`, error);
          return {
            userId: user.id,
            status: "error",
            error: error instanceof Error ? error.message : "Unknown error"
          };
        }
      })
    );

    return {
      processedUsers,
      successCount,
      errorCount,
      results: results.map(r => r.status === "fulfilled" ? r.value : { status: "error" })
    };

  } catch (error) {
    console.error("❌ Erreur critique lors du traitement des notifications push:", error);
    return { processedUsers, successCount, errorCount, results: [] };
  }
}

// Méthodes HTTP non autorisées
// Vercel Cron appelle en GET, donc on redirige vers la logique POST
export async function GET(request: NextRequest) {
  if (process.env.NODE_ENV === 'development') {
  console.log("🔄 Vercel Cron appelé en GET, redirection vers la logique POST");
  }
  return await POST(request);
}

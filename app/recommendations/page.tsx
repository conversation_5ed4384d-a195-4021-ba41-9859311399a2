import { getSession } from "@/lib/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { accounts, recommendations, wishlistItems, users } from "@/lib/db/schema";
import { and, eq, desc } from "drizzle-orm";
import { Loader2, Music, Sparkles } from "lucide-react";
import { getTranslations } from 'next-intl/server';
import Image from "next/image";
import RecommendationsClient from "./RecommendationsClient";
import { getUserDiscogsCollection, markOwnedAlbums } from "@/lib/album-matching";


interface SearchParams {
  timeframe?: string;
  hideOwned?: string;
  withOffers?: string;
  from?: string;
}

interface RecommendationsPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function RecommendationsPage({ searchParams }: RecommendationsPageProps) {
  const session = await getSession();
  const t = await getTranslations('recommendations');

  // Vérifier si l'utilisateur est connecté
  if (!session) {
    redirect("/login?callbackUrl=/recommendations");
  }

  // Vérifier si l'utilisateur a lié Spotify et Discogs
  let hasSpotifyAccount = false;
  let hasDiscogsAccount = false;
  const userId = (session.user as any)?.id;
  if (userId) {
    try {
      const spotifyAccount = await db.query.accounts.findFirst({
        where: and(
          eq(accounts.userId, userId),
          eq(accounts.provider, 'spotify')
        )
      });
      hasSpotifyAccount = !!spotifyAccount;

      const discogsAccount = await db.query.accounts.findFirst({
        where: and(
          eq(accounts.userId, userId),
          eq(accounts.provider, 'discogs')
        )
      });
      hasDiscogsAccount = !!discogsAccount;
    } catch (error) {
      console.error("Erreur lors de la vérification des comptes:", error);
    }
  }

  // Extraire et valider les paramètres de filtrage
  const params = await searchParams;
  const timeframe = params.timeframe || 'short_term';
  const hideOwned = params.hideOwned === 'true'; // Par défaut false (ne pas masquer les vinyles possédés)
  const withOffers = params.withOffers === 'true';
  const fromGenerating = params.from === 'generating';

  // Valider le timeframe
  const validTimeframes = ['short_term', 'medium_term', 'long_term'];
  const selectedTimeframe = validTimeframes.includes(timeframe) ? timeframe : 'short_term';

  // Récupérer les recommandations existantes (le client gérera la priorisation)
  let userRecommendations: any[] = [];
  let hasRecommendations = false;

  // Pour éviter le flash de l'état vide, on charge toujours quelques recommandations
  // Le client se chargera de la priorisation et de la mise à jour de l'URL
  if (userId && hasSpotifyAccount) {
    try {
      // Essayer de récupérer des recommandations dans l'ordre de priorité
      const timeframesToTry = ['short_term', 'medium_term', 'long_term'];

      for (const timeframe of timeframesToTry) {
        const allRecommendations = await db.query.recommendations.findMany({
          where: and(
            eq(recommendations.userId, userId),
            eq(recommendations.timeframe, timeframe)
          ),
          orderBy: [desc(recommendations.listenScore), desc(recommendations.generatedAt)],
          limit: 50,
        });

        if (allRecommendations.length > 0) {
          // Récupérer les albums en wishlist
          const wishlistedItems = await db.query.wishlistItems.findMany({
            where: eq(wishlistItems.userId, userId),
            columns: {
              artistName: true,
              albumTitle: true,
            },
          });

          const wishlistedSet = new Set(
            wishlistedItems.map(item => `${item.artistName}|${item.albumTitle}`)
          );

          // Marquer les albums possédés via Discogs
          let recommendationsWithCorrectOwnership = allRecommendations;
          try {
            const discogsCollection = await getUserDiscogsCollection(userId, db);

            if (discogsCollection.length > 0) {
              // Recalculer le statut isOwned en temps réel
              const recalculatedRecommendations = markOwnedAlbums(
                allRecommendations.map(rec => ({
                  artistName: rec.artistName,
                  albumTitle: rec.albumTitle,
                  spotifyAlbumId: rec.spotifyAlbumId || undefined,
                  albumCoverUrl: rec.albumCoverUrl || undefined,
                  listenScore: rec.listenScore,
                  timeframe: rec.timeframe
                })),
                discogsCollection
              );

              // Fusionner les données recalculées avec les données originales
              recommendationsWithCorrectOwnership = allRecommendations.map((original, index) => ({
                ...original,
                isOwned: recalculatedRecommendations[index].isOwned
              }));
            }
          } catch (discogsError) {
            if (process.env.NODE_ENV === 'development') {
            console.warn("⚠️ Erreur lors du recalcul du statut isOwned, utilisation des données en base:", discogsError);
            }
          }

          // Appliquer les filtres côté serveur
          let filteredRecommendations = recommendationsWithCorrectOwnership;

          // Filtre pour masquer les vinyles possédés (si hideOwned est true)
          if (hideOwned) {
            filteredRecommendations = filteredRecommendations.filter(rec => !rec.isOwned);
          }

          // Filtre pour afficher uniquement les recommandations avec offres
          if (withOffers) {
            filteredRecommendations = filteredRecommendations.filter(rec =>
              rec.affiliateLinks && Array.isArray(rec.affiliateLinks) && rec.affiliateLinks.length > 0
            );
          }

          if (filteredRecommendations.length > 0) {
            // Ajouter l'information isWishlisted à chaque recommandation
            userRecommendations = filteredRecommendations.map(rec => ({
              ...rec,
              isWishlisted: wishlistedSet.has(`${rec.artistName}|${rec.albumTitle}`),
            }));

            hasRecommendations = userRecommendations.length > 0;
            break; // Sortir de la boucle dès qu'on trouve des recommandations
          }
        }
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des recommandations:", error);
    }
  }



  // Préparer le nom d'utilisateur pour les traductions
  const userName = session.user?.name || session.user?.email || '';

  return (
    <div className="container py-12">
      <RecommendationsClient
        initialRecommendations={userRecommendations}
        hasSpotifyAccount={hasSpotifyAccount}
        hasDiscogsAccount={hasDiscogsAccount}
        userId={userId}
        pageTitle={t('title')}
        initialTimeframe={selectedTimeframe}
        initialHideOwned={hideOwned}
        initialWithOffers={withOffers}
        fromGenerating={fromGenerating}
      />
    </div>
  );
}

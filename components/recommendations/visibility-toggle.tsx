"use client";

import { useState, useTransition } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Lock, Unlock, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { setListVisibility } from "@/app/actions/recommendations";
import { ShareButton } from "./share-button";

interface VisibilityToggleProps {
  initialPublicListEnabled: boolean;
  publicListId: string;
  className?: string;
}

export function VisibilityToggle({ 
  initialPublicListEnabled, 
  publicListId,
  className 
}: VisibilityToggleProps) {
  const [isPublic, setIsPublic] = useState(initialPublicListEnabled);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [isPending, startTransition] = useTransition();

  const handleToggleChange = (checked: boolean) => {
    if (checked && !isPublic) {
      // Première fois que l'utilisateur veut rendre sa liste publique
      setShowConfirmDialog(true);
    } else {
      // Passage de public à privé - pas besoin de confirmation
      updateVisibility(checked);
    }
  };

  const updateVisibility = (newStatus: boolean) => {
    startTransition(async () => {
      try {
        const result = await setListVisibility(newStatus);

        if (result.success) {
          setIsPublic(newStatus);
          if (process.env.NODE_ENV === 'development') {
          console.log(`Liste ${newStatus ? 'publique' : 'privée'}`);
          }
        } else {
          console.error("Erreur lors de la mise à jour:", result.error);
          // Revenir à l'état précédent en cas d'erreur
          setIsPublic(!newStatus);
        }
      } catch (error) {
        console.error("Erreur lors de la mise à jour de la visibilité:", error);
        // Revenir à l'état précédent en cas d'erreur
        setIsPublic(!newStatus);
      }
    });
  };

  const handleConfirmPublic = () => {
    setShowConfirmDialog(false);
    updateVisibility(true);
  };

  const handleCancelPublic = () => {
    setShowConfirmDialog(false);
    // Le switch reste sur false
  };

  // La logique de partage est maintenant gérée par le composant ShareButton

  return (
    <>
      <div className={cn("flex items-center space-x-4", className)}>
        {/* Contrôle de visibilité */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            {isPublic ? (
              <Unlock className="h-4 w-4 text-green-600" />
            ) : (
              <Lock className="h-4 w-4 text-muted-foreground" />
            )}
            <Label 
              htmlFor="visibility-toggle" 
              className="text-sm font-medium cursor-pointer"
            >
              Liste Publique
            </Label>
          </div>
          
          <Switch
            id="visibility-toggle"
            checked={isPublic}
            onCheckedChange={handleToggleChange}
            disabled={isPending}
            className="data-[state=checked]:bg-green-600"
          />
          
          {isPending && (
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          )}
        </div>

        {/* Bouton Partager - apparaît avec animation quand la liste est publique */}
        {isPublic && (
          <ShareButton
            publicListId={publicListId}
            className={isPending ? "opacity-50 pointer-events-none" : ""}
          />
        )}
      </div>

      {/* Modale de confirmation */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Rendre votre liste publique ?</AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p>
                En rendant votre liste publique, n'importe qui disposant du lien pourra voir :
              </p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Vos recommandations d'albums</li>
                <li>Votre nom et votre photo de profil</li>
                <li>Vos goûts musicaux</li>
              </ul>
              <p className="text-sm text-muted-foreground mt-3">
                Vous pourrez revenir en mode privé à tout moment.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelPublic}>
              Annuler
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleConfirmPublic}
              className="bg-green-600 hover:bg-green-700"
            >
              Rendre Publique
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

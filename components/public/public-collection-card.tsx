"use client";

import { useState } from "react";
import { Session } from "next-auth";
import { Music, ExternalLink } from "lucide-react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { WishlistButton } from "./wishlist-button";
import { useTranslations } from 'next-intl';

interface CollectionItem {
  id: number;
  discogsReleaseId: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  year?: number | null;
  format?: string | null;
  syncedAt: Date;
}

interface AlbumData {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  spotifyAlbumId?: string | null;
  discogsReleaseId?: number | null;
}

interface PublicCollectionCardProps {
  item: CollectionItem;
  session: Session | null;
  isWishlisted: boolean;
  onHeartClick: (albumData: AlbumData) => void;
}

export function PublicCollectionCard({
  item,
  session,
  isWishlisted,
  onHeartClick
}: PublicCollectionCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const t = useTranslations('public.collection');

  const albumData: AlbumData = {
    artistName: item.artistName,
    albumTitle: item.albumTitle,
    albumCoverUrl: item.albumCoverUrl,
    discogsReleaseId: item.discogsReleaseId,
  };

  return (
    <div 
      className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 overflow-hidden hover:shadow-lg transition-all duration-300 group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Image de l'album */}
      <div className="aspect-square relative">
        {item.albumCoverUrl ? (
          <Image
            src={item.albumCoverUrl}
            alt={`${item.albumTitle} by ${item.artistName}`}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 flex items-center justify-center">
            <Music className="w-16 h-16 text-slate-400 dark:text-slate-500" />
          </div>
        )}

        {/* Bouton wishlist en haut à gauche */}
        <WishlistButton
          albumData={albumData}
          isAuthenticated={!!session}
          isWishlisted={isWishlisted}
          publicListId=""
          className="absolute top-2 left-2"
        />

        {/* Tag format en haut à droite */}
        {item.format && (
          <div className="absolute top-2 right-2 bg-slate-800/80 text-white text-xs px-2 py-1 rounded-full">
            {item.format}
          </div>
        )}
      </div>

      {/* Informations de l'album */}
      <div className="p-4">
        <h3 className="font-semibold text-slate-900 dark:text-white mb-1 line-clamp-2">
          {item.albumTitle}
        </h3>
        <p className="text-sm text-slate-600 dark:text-slate-300 line-clamp-1">
          {item.artistName}
        </p>

        {/* Année de sortie */}
        {item.year && (
          <p className="text-sm text-slate-600 dark:text-slate-300 mb-3">
            {item.year}
          </p>
        )}

        {/* Bouton d'achat Amazon */}
        <div className="flex justify-end mb-3">
          <button
            className="text-xs bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-1.5 rounded-full transition-colors flex items-center gap-1.5"
            onClick={(e) => {
              e.stopPropagation();
              const searchQuery = encodeURIComponent(`${item.artistName} ${item.albumTitle} vinyl`);
              const amazonSearchUrl = `https://www.amazon.fr/s?k=${searchQuery}&tag=stream2spin-21`;
              window.open(amazonSearchUrl, '_blank', 'noopener,noreferrer');
            }}
            title={`Rechercher "${item.artistName} ${item.albumTitle}" sur Amazon`}
          >
            <Image
              src="/vendors/Amazon_icon_white.svg"
              alt="Amazon"
              width={14}
              height={14}
              className="w-3.5 h-3.5"
            />
            {t('buy')}
          </button>
        </div>


      </div>
    </div>
  );
}

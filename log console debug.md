recommendations:1  GET http://127.0.0.1:3000/recommendations 500 (Internal Server Error)
react-dom-client.development.js:25022 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
auth.ts:285  Server  🚀 Cache DB hit en 0ms pour: 33d92260-869f-4d7b-aac2-94ff6b03c240
auth.ts:285  Server  🚀 Cache DB hit en 0ms pour: 33d92260-869f-4d7b-aac2-94ff6b03c240
logger.ts:15  Server  Query: select "preferredLanguage" from "users" where "users"."id" = $1 limit $2 -- params: ["33d92260-869f-4d7b-aac2-94ff6b03c240", 1]
logger.ts:15  Server  Query: select "userId", "type", "provider", "providerAccountId", "refresh_token", "access_token", "access_token_secret", "expires_at", "token_type", "scope", "id_token", "session_state" from "accounts" "accounts" where ("accounts"."userId" = $1 and "accounts"."provider" = $2) limit $3 -- params: ["33d92260-869f-4d7b-aac2-94ff6b03c240", "spotify", 1]
logger.ts:15  Server  Query: select "userId", "type", "provider", "providerAccountId", "refresh_token", "access_token", "access_token_secret", "expires_at", "token_type", "scope", "id_token", "session_state" from "accounts" "accounts" where ("accounts"."userId" = $1 and "accounts"."provider" = $2) limit $3 -- params: ["33d92260-869f-4d7b-aac2-94ff6b03c240", "discogs", 1]
logger.ts:15  Server  Query: select "id", "userId", "artistName", "albumTitle", "albumCoverUrl", "discogsReleaseId", "spotifyAlbumId", "listenScore", "estimatedPlays", "timeframe", "generatedAt", "isOwned", "affiliateLinks", "topTrackName", "topTrackId", "topTrackPreviewUrl", "topTrackListenScore" from "recommendations" "recommendations" where ("recommendations"."userId" = $1 and "recommendations"."timeframe" = $2) order by "recommendations"."listenScore" desc, "recommendations"."generatedAt" desc limit $3 -- params: ["33d92260-869f-4d7b-aac2-94ff6b03c240", "short_term", 50]
logger.ts:15  Server  Query: select "artistName", "albumTitle" from "wishlist_items" "wishlistItems" where "wishlistItems"."userId" = $1 -- params: ["33d92260-869f-4d7b-aac2-94ff6b03c240"]
album-matching.ts:293  Server  🚀 Collection Discogs récupérée depuis le cache pour l'utilisateur 33d92260-869f-4d7b-aac2-94ff6b03c240
client-entry.tsx:13 WebSocket connection to 'ws://127.0.0.1:3000/_next/webpack-hmr' failed: 
createRootLevelDevOverlayElement @ client-entry.tsx:13
hydrate @ app-index.tsx:277
(anonymous) @ app-next-turbopack.ts:12
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
page.tsx:175 ReferenceError: SharePopover is not defined
    at RecommendationsClient (RecommendationsClient.tsx:333:14)
    at react-stack-bottom-frame (react-dom-client.development.js:22973:20)
    at renderWithHooks (react-dom-client.development.js:6666:22)
    at updateFunctionComponent (react-dom-client.development.js:8930:19)
    at beginWork (react-dom-client.development.js:10504:35)
    at runWithFiberInDEV (react-dom-client.development.js:844:30)
    at performUnitOfWork (react-dom-client.development.js:15257:22)
    at workLoopSync (react-dom-client.development.js:15077:41)
    at renderRootSync (react-dom-client.development.js:15057:11)
    at performWorkOnRoot (react-dom-client.development.js:14568:44)
    at performWorkOnRootViaSchedulerTask (react-dom-client.development.js:16349:7)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:45:48)

The above error occurred in the <RecommendationsClient> component. It was handled by the <ErrorBoundaryHandler> error boundary.
onCaughtError @ error-boundary-callbacks.ts:80
logCaughtError @ react-dom-client.development.js:8400
runWithFiberInDEV @ react-dom-client.development.js:844
update.callback @ react-dom-client.development.js:8433
callCallback @ react-dom-client.development.js:6428
commitCallbacks @ react-dom-client.development.js:6448
runWithFiberInDEV @ react-dom-client.development.js:844
commitClassCallbacks @ react-dom-client.development.js:12139
commitLayoutEffectOnFiber @ react-dom-client.development.js:12763
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12691
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12802
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12802
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12802
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12802
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12691
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12691
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12691
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12691
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12686
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12866
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12691
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13672
commitLayoutEffectOnFiber @ react-dom-client.development.js:12768
flushLayoutEffects @ react-dom-client.development.js:15686
commitRoot @ react-dom-client.development.js:15527
onUnsuspend @ react-dom-client.development.js:20545
"use client"
RecommendationsPage @ page.tsx:175
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2348
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
readChunk @ react-server-dom-turbopack-client.browser.development.js:942
react-stack-bottom-frame @ react-dom-client.development.js:23077
createChild @ react-dom-client.development.js:5382
reconcileChildrenArray @ react-dom-client.development.js:5689
reconcileChildFibersImpl @ react-dom-client.development.js:6012
(anonymous) @ react-dom-client.development.js:6117
reconcileChildren @ react-dom-client.development.js:8654
beginWork @ react-dom-client.development.js:10903
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<RecommendationsPage>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
error.tsx:18 Application error: {message: 'SharePopover is not defined', digest: undefined, timestamp: '2025-07-21T02:17:22.203Z'}
error @ intercept-console-error.ts:40
Error.useEffect @ error.tsx:18
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
performSyncWorkOnRoot @ react-dom-client.development.js:16361
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
<Error>
exports.jsx @ react-jsx-runtime.development.js:339
render @ error-boundary.tsx:143
react-stack-bottom-frame @ react-dom-client.development.js:22986
updateClassComponent @ react-dom-client.development.js:9487
beginWork @ react-dom-client.development.js:10569
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<ErrorBoundaryHandler>
exports.jsx @ react-jsx-runtime.development.js:339
ErrorBoundary @ error-boundary.tsx:209
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<ErrorBoundary>
exports.jsx @ react-jsx-runtime.development.js:339
OuterLayoutRouter @ layout-router.tsx:603
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
readChunk @ react-server-dom-turbopack-client.browser.development.js:942
react-stack-bottom-frame @ react-dom-client.development.js:23077
createChild @ react-dom-client.development.js:5382
reconcileChildrenArray @ react-dom-client.development.js:5689
reconcileChildFibersImpl @ react-dom-client.development.js:6012
(anonymous) @ react-dom-client.development.js:6117
reconcileChildren @ react-dom-client.development.js:8654
beginWork @ react-dom-client.development.js:10754
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
preload-critical-routes.ts:63 🚀 Routes critiques pré-chargées côté client
preload-critical-routes.ts:88 🚀 DNS pré-chargé pour les domaines critiques
use-websocket.ts:16 WebSocket connection to 'ws://127.0.0.1:3000/_next/webpack-hmr' failed: 
(anonymous) @ use-websocket.ts:16
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
performSyncWorkOnRoot @ react-dom-client.development.js:16361
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
stack-frame.ts:95  POST http://127.0.0.1:3000/__nextjs_original-stack-frames 403 (Forbidden)
getOriginalStackFrames @ stack-frame.ts:95
(anonymous) @ get-error-by-type.ts:58
createMemoizedPromise @ get-error-by-type.ts:97
getErrorByType @ get-error-by-type.ts:57
(anonymous) @ render-error.tsx:97
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
<RenderRuntimeError>
exports.jsx @ react-jsx-runtime.development.js:339
RenderError @ render-error.tsx:49
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
<RenderError>
exports.jsx @ react-jsx-runtime.development.js:339
DevOverlay @ dev-overlay.tsx:34
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
<DevOverlay>
exports.jsx @ react-jsx-runtime.development.js:339
AppDevOverlay @ app-dev-overlay.tsx:97
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<AppDevOverlay>
exports.jsx @ react-jsx-runtime.development.js:339
HotReload @ hot-reloader-client.tsx:631
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<HotReload>
exports.jsx @ react-jsx-runtime.development.js:339
Router @ app-router.tsx:510
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<Router>
exports.jsx @ react-jsx-runtime.development.js:339
AppRouter @ app-router.tsx:571
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<AppRouter>
exports.jsx @ react-jsx-runtime.development.js:339
ServerRoot @ app-index.tsx:171
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<ServerRoot>
exports.jsx @ react-jsx-runtime.development.js:339
hydrate @ app-index.tsx:263
(anonymous) @ app-next-turbopack.ts:12
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
stack-frame.ts:95  POST http://127.0.0.1:3000/__nextjs_original-stack-frames 403 (Forbidden)
getOriginalStackFrames @ stack-frame.ts:95
(anonymous) @ get-error-by-type.ts:58
createMemoizedPromise @ get-error-by-type.ts:97
getErrorByType @ get-error-by-type.ts:57
(anonymous) @ render-error.tsx:97
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:847
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
<RenderRuntimeError>
exports.jsx @ react-jsx-runtime.development.js:339
RenderError @ render-error.tsx:49
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
<RenderError>
exports.jsx @ react-jsx-runtime.development.js:339
DevOverlay @ dev-overlay.tsx:34
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
<DevOverlay>
exports.jsx @ react-jsx-runtime.development.js:339
AppDevOverlay @ app-dev-overlay.tsx:97
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<AppDevOverlay>
exports.jsx @ react-jsx-runtime.development.js:339
HotReload @ hot-reloader-client.tsx:631
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<HotReload>
exports.jsx @ react-jsx-runtime.development.js:339
Router @ app-router.tsx:510
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<Router>
exports.jsx @ react-jsx-runtime.development.js:339
AppRouter @ app-router.tsx:571
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<AppRouter>
exports.jsx @ react-jsx-runtime.development.js:339
ServerRoot @ app-index.tsx:171
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<ServerRoot>
exports.jsx @ react-jsx-runtime.development.js:339
hydrate @ app-index.tsx:263
(anonymous) @ app-next-turbopack.ts:12
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
stack-frame.ts:95  POST http://127.0.0.1:3000/__nextjs_original-stack-frames 403 (Forbidden)
getOriginalStackFrames @ stack-frame.ts:95
(anonymous) @ get-error-by-type.ts:58
createMemoizedPromise @ get-error-by-type.ts:97
getErrorByType @ get-error-by-type.ts:57
(anonymous) @ render-error.tsx:97
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<RenderRuntimeError>
exports.jsx @ react-jsx-runtime.development.js:339
RenderError @ render-error.tsx:49
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
<RenderError>
exports.jsx @ react-jsx-runtime.development.js:339
DevOverlay @ dev-overlay.tsx:34
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
<DevOverlay>
exports.jsx @ react-jsx-runtime.development.js:339
AppDevOverlay @ app-dev-overlay.tsx:97
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<AppDevOverlay>
exports.jsx @ react-jsx-runtime.development.js:339
HotReload @ hot-reloader-client.tsx:631
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<HotReload>
exports.jsx @ react-jsx-runtime.development.js:339
Router @ app-router.tsx:510
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<Router>
exports.jsx @ react-jsx-runtime.development.js:339
AppRouter @ app-router.tsx:571
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<AppRouter>
exports.jsx @ react-jsx-runtime.development.js:339
ServerRoot @ app-index.tsx:171
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<ServerRoot>
exports.jsx @ react-jsx-runtime.development.js:339
hydrate @ app-index.tsx:263
(anonymous) @ app-next-turbopack.ts:12
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
favicon.ico:1  GET http://127.0.0.1:3000/favicon.ico 404 (Not Found)
stack-frame.ts:95  POST http://127.0.0.1:3000/__nextjs_original-stack-frames 403 (Forbidden)
getOriginalStackFrames @ stack-frame.ts:95
(anonymous) @ get-error-by-type.ts:58
createMemoizedPromise @ get-error-by-type.ts:97
getErrorByType @ get-error-by-type.ts:57
(anonymous) @ render-error.tsx:97
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<RenderRuntimeError>
exports.jsx @ react-jsx-runtime.development.js:339
RenderError @ render-error.tsx:49
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
<RenderError>
exports.jsx @ react-jsx-runtime.development.js:339
DevOverlay @ dev-overlay.tsx:34
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
<DevOverlay>
exports.jsx @ react-jsx-runtime.development.js:339
AppDevOverlay @ app-dev-overlay.tsx:97
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<AppDevOverlay>
exports.jsx @ react-jsx-runtime.development.js:339
HotReload @ hot-reloader-client.tsx:631
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<HotReload>
exports.jsx @ react-jsx-runtime.development.js:339
Router @ app-router.tsx:510
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<Router>
exports.jsx @ react-jsx-runtime.development.js:339
AppRouter @ app-router.tsx:571
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<AppRouter>
exports.jsx @ react-jsx-runtime.development.js:339
ServerRoot @ app-index.tsx:171
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14568
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<ServerRoot>
exports.jsx @ react-jsx-runtime.development.js:339
hydrate @ app-index.tsx:263
(anonymous) @ app-next-turbopack.ts:12
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
preload-critical-routes.ts:59  GET http://127.0.0.1:3000/recommendations net::ERR_ABORTED 500 (Internal Server Error)
(anonymous) @ preload-critical-routes.ts:59
preloadCriticalRoutes @ preload-critical-routes.ts:55
AuthenticatedLayout.useEffect @ authenticated-layout.tsx:20
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
performSyncWorkOnRoot @ react-dom-client.development.js:16361
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
"use client"
RootLayout @ layout.tsx:61
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2348
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
readChunk @ react-server-dom-turbopack-client.browser.development.js:942
react-stack-bottom-frame @ react-dom-client.development.js:23077
createChild @ react-dom-client.development.js:5382
reconcileChildrenArray @ react-dom-client.development.js:5689
reconcileChildFibersImpl @ react-dom-client.development.js:6012
(anonymous) @ react-dom-client.development.js:6117
reconcileChildren @ react-dom-client.development.js:8654
beginWork @ react-dom-client.development.js:10754
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<RootLayout>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
preload-critical-routes.ts:59  GET http://127.0.0.1:3000/wishlist net::ERR_ABORTED 500 (Internal Server Error)
(anonymous) @ preload-critical-routes.ts:59
preloadCriticalRoutes @ preload-critical-routes.ts:55
AuthenticatedLayout.useEffect @ authenticated-layout.tsx:20
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
performSyncWorkOnRoot @ react-dom-client.development.js:16361
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
"use client"
RootLayout @ layout.tsx:61
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2348
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
readChunk @ react-server-dom-turbopack-client.browser.development.js:942
react-stack-bottom-frame @ react-dom-client.development.js:23077
createChild @ react-dom-client.development.js:5382
reconcileChildrenArray @ react-dom-client.development.js:5689
reconcileChildFibersImpl @ react-dom-client.development.js:6012
(anonymous) @ react-dom-client.development.js:6117
reconcileChildren @ react-dom-client.development.js:8654
beginWork @ react-dom-client.development.js:10754
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<RootLayout>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
preload-critical-routes.ts:59  GET http://127.0.0.1:3000/collection net::ERR_ABORTED 500 (Internal Server Error)
(anonymous) @ preload-critical-routes.ts:59
preloadCriticalRoutes @ preload-critical-routes.ts:55
AuthenticatedLayout.useEffect @ authenticated-layout.tsx:20
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
performSyncWorkOnRoot @ react-dom-client.development.js:16361
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushSpawnedWork @ react-dom-client.development.js:15804
commitRoot @ react-dom-client.development.js:15528
onUnsuspend @ react-dom-client.development.js:20545
"use client"
RootLayout @ layout.tsx:61
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2348
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
readChunk @ react-server-dom-turbopack-client.browser.development.js:942
react-stack-bottom-frame @ react-dom-client.development.js:23077
createChild @ react-dom-client.development.js:5382
reconcileChildrenArray @ react-dom-client.development.js:5689
reconcileChildFibersImpl @ react-dom-client.development.js:6012
(anonymous) @ react-dom-client.development.js:6117
reconcileChildren @ react-dom-client.development.js:8654
beginWork @ react-dom-client.development.js:10754
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<RootLayout>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
recommendations:1 The resource http://127.0.0.1:3000/_next/static/chunks/%5Broot-of-the-server%5D__d5cad332._.css was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.

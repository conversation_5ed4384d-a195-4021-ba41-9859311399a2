/**
 * Template d'email pour les notifications de recommandations
 * Epic 6 - Notifications Utilisateur
 */

import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Row,
  Column,
  Heading,
  Text,
  Link,
  Img,
  Button,
  Hr,
  Preview,
} from '@react-email/components';
import { EMAIL_ASSETS } from '@/lib/email-assets';
import { EmailHeader } from '@/components/emails/EmailHeader';

interface EmailRecommendation {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string;
  listenScore: number;
  affiliateLinks?: Array<{
    vendor: string;
    url: string;
    price?: number;
    currency?: string;
  }>;
  isOwned: boolean;
}

interface EmailUserData {
  id: string;
  name?: string;
  email: string;
  preferredLanguage: string;
}

interface RecommendationsEmailProps {
  user: EmailUserData;
  personalRecommendations: EmailRecommendation[];
  communityRecommendations?: EmailRecommendation[];
  totalCount: number;
  unsubscribeUrl: string;
  viewRecommendationsUrl: string;
  t: (key: string, values?: Record<string, any>) => string;
}

const RecommendationList = ({
  recommendations,
  t,
}: {
  recommendations: EmailRecommendation[];
  t: (key: string, values?: Record<string, any>) => string;
}) => (
  <>
    {recommendations.map((rec, index) => (
      <Section key={index} style={recommendationCard}>
        <Row>
          <Column style={albumCoverColumn}>
            {rec.albumCoverUrl && (
              <Img
                src={rec.albumCoverUrl}
                width="80"
                height="80"
                alt={`${rec.artistName} - ${rec.albumTitle}`}
                style={albumCover}
              />
            )}
          </Column>
          <Column style={albumInfoColumn}>
            <Text style={albumTitle}>{rec.albumTitle}</Text>
            <Text style={artistName}>{rec.artistName}</Text>
            {rec.affiliateLinks && rec.affiliateLinks.length > 0 && (
              <div style={buyLinksContainer}>
                {rec.affiliateLinks.slice(0, 2).map((link, linkIndex) => (
                  <Button
                    key={linkIndex}
                    href={link.url}
                    style={buyButton}
                  >
                    {t('emails.recommendations.buy_on')} {link.vendor}
                    {link.price && ` - ${link.price}${link.currency}`}
                  </Button>
                ))}
              </div>
            )}
          </Column>
          <Column style={wishlistColumn}>
            <Button
              href={`${process.env.NEXTAUTH_URL}/api/wishlist/add-from-email?artist=${encodeURIComponent(rec.artistName)}&album=${encodeURIComponent(rec.albumTitle)}`}
              style={heartButton}
              title={t('emails.recommendations.add_wishlist')}
            >
              <Img
                src={EMAIL_ASSETS.heartIcon}
                width="16"
                height="16"
                alt="♥"
                style={heartIcon}
              />
            </Button>
          </Column>
        </Row>
      </Section>
    ))}
  </>
);

export function RecommendationsEmailTemplate({
  user,
  personalRecommendations,
  communityRecommendations,
  totalCount,
  unsubscribeUrl,
  viewRecommendationsUrl,
  t,
}: RecommendationsEmailProps) {
  return (
    <Html>
      <Head>
        {/* Meta tags pour améliorer la délivrabilité */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta httpEquiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="x-apple-disable-message-reformatting" />
        <meta name="format-detection" content="telephone=no,address=no,email=no,date=no,url=no" />
        {/* Titre pour les clients de messagerie */}
        <title>{t('emails.recommendations.preview', { totalCount })}</title>
      </Head>
      <Preview>{t('emails.recommendations.preview', { totalCount })}</Preview>
      <Body style={main}>
        <Container style={container}>
          {/* Header avec logo */}
          <EmailHeader />

          {/* Contenu principal */}
          <Section style={content}>
            <Heading style={h2}>{t('emails.recommendations.greeting', { name: user.name || 'Music Lover' })}</Heading>
            <Text style={text}>{t('emails.recommendations.intro', { totalCount })}</Text>

            {communityRecommendations && communityRecommendations.length > 0 ? (
              <>
                <Heading style={h3}>{t('emails.recommendations.personal_title')}</Heading>
                <RecommendationList recommendations={personalRecommendations} t={t} />
                <Hr style={hr} />
                <Heading style={h3}>{t('emails.recommendations.community_title')}</Heading>
                <RecommendationList recommendations={communityRecommendations} t={t} />
              </>
            ) : (
              <RecommendationList recommendations={personalRecommendations} t={t} />
            )}

            {/* Texte "Et bien plus encore !" et bouton pour voir toutes les recommandations */}
            <Section style={ctaSection}>
              <Text style={moreText}>
                {t('emails.recommendations.more_to_discover')}
              </Text>
              <Button href={viewRecommendationsUrl} style={primaryButton}>
                {t('emails.recommendations.view_all')}
              </Button>
            </Section>

            <Hr style={hr} />

            {/* Footer optimisé pour la délivrabilité */}
            <Section style={footer}>
              <Text style={footerText}>{t('emails.recommendations.footer_info')}</Text>

              {/* Liens de désabonnement conformes aux standards */}
              <Text style={footerText}>
                <Link href={unsubscribeUrl} style={link}>
                  {t('emails.recommendations.unsubscribe')}
                </Link>
                {' | '}
                <Link
                  href={`${process.env.NEXTAUTH_URL}/api/unsubscribe?email=${encodeURIComponent(user.email)}&type=one-click`}
                  style={link}
                >
                  {t('emails.recommendations.unsubscribe')}
                </Link>
              </Text>

              {/* Informations légales */}
              <Text style={footerText}>
                {t('emails.recommendations.company_info')}
                <br />
                {t('emails.recommendations.footer_info')}
              </Text>

              <Text style={footerText}>{t('emails.recommendations.powered_by')}</Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles CSS-in-JS optimisés pour la délivrabilité
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  margin: '0',
  padding: '24px 0',
  width: '100%',
  wordSpacing: 'normal',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '0 0 48px',
  marginBottom: '64px',
  maxWidth: '600px', // Largeur maximale pour la compatibilité
  width: '100%',
  border: '1px solid #e6e6e6', // Bordure subtile pour la structure
  borderRadius: '12px',
};



const h1 = {
  color: '#ffffff',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '8px 0 0 0',
  padding: '0',
  lineHeight: '32px',
};

const content = {
  padding: '24px',
};

const h2 = {
  color: '#333',
  fontSize: '20px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  padding: '0',
};

const h3 = {
  color: '#333',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '24px 0 16px 0',
  padding: '0',
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 24px 0',
};

const recommendationCard = {
  border: '1px solid #e6e6e6',
  borderRadius: '8px',
  padding: '16px',
  marginBottom: '16px',
  backgroundColor: '#fafafa',
};

const albumCoverColumn = {
  width: '80px',
  verticalAlign: 'top' as const,
};

const albumInfoColumn = {
  paddingLeft: '16px',
  verticalAlign: 'top' as const,
};

const wishlistColumn = {
  width: '40px',
  textAlign: 'center' as const,
  verticalAlign: 'top' as const,
  paddingLeft: '8px',
};

const albumCover = {
  borderRadius: '4px',
};

const albumTitle = {
  color: '#333',
  fontSize: '16px',
  fontWeight: 'bold',
  margin: '0 0 4px 0',
  lineHeight: '20px',
};

const artistName = {
  color: '#666',
  fontSize: '14px',
  margin: '0 0 8px 0',
  lineHeight: '18px',
};

const buyLinksContainer = {
  marginTop: '8px',
};

const buyButton = {
  backgroundColor: '#6236FF',
  borderRadius: '4px',
  color: '#fff',
  fontSize: '12px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '8px 12px',
  margin: '0 8px 4px 0',
};

const heartButton = {
  backgroundColor: 'transparent',
  border: 'none',
  borderRadius: '4px',
  color: '#ff4757',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '4px',
  width: 'auto',
  height: 'auto',
  lineHeight: '16px',
};

const heartIcon = {
  // Pas de filtre - garde la couleur rouge naturelle de l'icône
};

const ctaSection = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const moreText = {
  color: '#333',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  textAlign: 'center' as const,
};

const primaryButton = {
  backgroundColor: '#6236FF',
  borderRadius: '8px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
};

const hr = {
  borderColor: '#e6e6e6',
  margin: '32px 0',
};

const footer = {
  textAlign: 'center' as const,
};

const footerText = {
  color: '#666',
  fontSize: '12px',
  lineHeight: '16px',
  margin: '0 0 8px 0',
};

const link = {
  color: '#6236FF',
  textDecoration: 'underline',
};

export default RecommendationsEmailTemplate;

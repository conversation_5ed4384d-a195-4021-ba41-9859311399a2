{"account": {"title": "Mon Compte", "profile": {"title": "Profil", "description": "Informations de votre compte Spotify. Ces informations sont en lecture seule.", "username": "Nom d'utilisateur", "email": "<PERSON><PERSON><PERSON> email", "profilePicture": "Photo de profil", "syncedFromSpotify": "Photo synchronisée depuis votre compte Spotify", "notDefined": "Non défini"}, "connectedAccounts": {"title": "Comptes Connectés", "description": "Gérez vos connexions aux services externes pour améliorer vos recommandations.", "spotify": {"name": "Spotify", "connected": "Connecté", "tooltip": "Ceci est votre méthode de connexion principale et ne peut être déconnecté."}, "discogs": {"name": "Discogs", "connected": "Connecté", "notConnected": "Non connecté", "connect": "Connecter", "disconnect": "Déconnecter", "tooltip": "É<PERSON><PERSON> de recommander des vinyles que vous possédez déjà dans votre collection.", "description": "Connectez votre compte Discogs pour que nous puissions identifier les vinyles que vous possédez déjà et ainsi éviter les recommandations en double.", "refreshTooltip": "Resynchronise ta collection Discogs pour des recommandations à jour", "disconnectDialog": {"title": "Déconnecter votre compte Discogs ?", "description": "Cette action supprimera la liaison avec votre compte Discogs et toutes les données de collection mises en cache. Vos futures recommandations pourraient inclure des albums que vous possédez déjà.", "cancel": "Annuler", "confirm": "Confirmer la déconnexion", "disconnecting": "Déconnexion..."}}, "syncStatus": {"spotify": "Synchronisation Spotify", "discogs": "Synchronisation Discogs", "lastSync": "Dernière synchronisation", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "refreshing": "Synchronisation en cours...", "refreshDescription": "Resynchronise tes écoutes Spotify et génère de nouvelles recommandations", "never": "<PERSON><PERSON> synchroni<PERSON>", "justNow": "À l'instant", "minutesAgo": "Il y a {minutes} min", "hoursAgo": "Il y a {hours}h", "daysAgo": "Il y a {days}j", "recommendations": "{count} recommandations"}}, "visibility": {"title": "Visibilité & Partage", "description": "Contrôlez qui peut voir votre profil et ce que vous partagez.", "level": {"title": "Niveau de visibilité du profil", "public": {"label": "Public", "description": "Les personnes ayant le lien vers votre profil peuvent y accéder, même sans être connecté à Stream2Spin."}, "users_only": {"label": "Utilisateurs Stream2Spin", "description": "Seuls les utilisateurs connectés peuvent voir votre profil."}, "private": {"label": "Priv<PERSON>", "description": "Se<PERSON> vous pouvez voir votre profil."}}, "sharing": {"title": "Contrôles de partage", "recommendations": "Partager mes recommandations", "wishlist": "Partager ma wishlist", "collection": "Partager ma collection"}, "privateWarning": "Votre profil est actuellement privé. Pour le partager, veuillez changer sa visibilité à 'Public' ou 'Utilisateurs Stream2Spin'.", "save": "Enregistrer", "saving": "Enregistrement...", "updateSuccess": "Paramètres de visibilité mis à jour.", "updateError": "<PERSON><PERSON>ur lors de la mise à jour."}, "notifications": {"title": "Préférences de Notification", "description": "Choisissez la fréquence à laquelle vous souhaitez recevoir des communications.", "email": "Notifications par Email", "push": "Notifications Push", "frequency": {"weekly": "<PERSON><PERSON>", "biweekly": "Toutes les 2 semaines", "monthly": "<PERSON><PERSON> mois", "never": "<PERSON><PERSON>"}, "newFollower": {"label": "Quand quelqu'un me suit", "description": "Recevoir un email à chaque nouvel abonné."}}, "language": {"title": "Langue de l'Interface", "description": "Choisissez votre langue préférée pour l'application.", "label": "<PERSON><PERSON>", "placeholder": "Sélectionnez une langue", "updating": "Mise à jour de la langue..."}, "dangerZone": {"title": "Zone de Danger", "description": "Actions irréversibles concernant votre compte. Procédez avec prudence.", "deleteAccount": {"title": "Supprimer le compte", "description": "Supprime définitivement votre compte et toutes vos données personnelles.", "button": "Supprimer le compte", "dialog": {"title": "Êtes-vous absolument sûr ?", "description": "Cette action est **irréversible**. Elle supprimera définitivement votre compte et toutes vos données personnelles.", "dataList": ["Toutes vos recommandations personnalisées", "Votre liste d'envies (wishlist)", "Vos connexions aux comptes externes (Spotify, Discogs)", "Votre historique de synchronisation", "Toutes vos préférences et paramètres"], "confirmText": "Pour confirmer, tapez \"supprimer mon compte\" ci-dessous :", "placeholder": "supprimer mon compte", "cancel": "Annuler", "confirm": "Supprimer définitivement", "deleting": "Suppression..."}}}, "deleteConfirmationLabel": "Texte de confirmation"}, "Notifications": {"title": "Notifications", "description": "Vos dernières mises à jour.", "noNotifications": "Vous n'avez aucune nouvelle notification.", "newFollower": "{name} vous suit maintenant."}, "navigation": {"myAccount": "Mon compte", "logout": "Déconnexion", "user": "Utilisa<PERSON>ur", "recommendations": "Recommandations", "social": "Social", "wishlist": "Mes envies", "collection": "Ma collection", "toggleMenu": "Basculer le menu de navigation"}, "auth": {"logout": "Se déconnecter", "loggingOut": "Déconnexion...", "logoutError": "Erreur lors de la déconnexion:"}, "common": {"loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "cancel": "Annuler", "confirm": "Confirmer", "save": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "close": "<PERSON><PERSON><PERSON>", "buy": "<PERSON><PERSON><PERSON>", "share": "Partager", "searchPlaceholder": "Recherche bientôt disponible !", "sortBy": "Trier par", "topTrack": "Votre titre phare :"}, "recommendations": {"title": "Vos Recommandations", "welcome": "Bienvenue {name} !", "welcomeWithEmail": "Bienvenue {email} !", "subtitle": "Voici vos recommandations de vinyles personnalisées.", "noRecommendationsYet": "Vos recommandations de vinyles personnalisées apparaîtront ici.", "analyzing": {"title": "Nous analysons votre profil Spotify...", "subtitle": "Vos premières recommandations de vinyles seront prêtes dans un instant !", "status": "En cours d'analyse", "steps": {"analyzing": "<PERSON><PERSON><PERSON> de vos goûts musicaux", "searching": "Recherche de vinyles correspondants", "generating": "Génération des recommandations"}, "autoUpdate": "Cette page se mettra à jour automatiquement une fois l'analyse terminée."}, "filters": {"filterList": "Filtrer la liste", "show": "<PERSON><PERSON><PERSON><PERSON>", "hide": "Masquer", "noSpotify": {"message": "🎵 Connectez votre compte Spotify pour commencer à recevoir des recommandations"}, "timeframe": {"title": "Période d'écoute", "shortTerm": "4 dernières semaines", "mediumTerm": "6 derniers mois", "longTerm": "12 derniers mois", "description": "Filtrez vos recommandations selon la période d'analyse de vos écoutes Spotify"}, "owned": {"title": "<PERSON>yle<PERSON> poss<PERSON>", "connectDiscogs": "Connecter Discogs", "connectDescription": "Connectez votre compte Discogs pour masquer les vinyles que vous possédez déjà", "hideOwned": "Masquer les albums déjà présents dans la collection", "showOwned": "Afficher les possédés", "hidingOwnedDescription": "Les vinyles de votre collection Discogs sont masqués", "showingOwnedDescription": "Les vinyles de votre collection Discogs sont affichés avec un badge"}, "activeTags": {"inCollection": "Dans la collection", "withOffers": "Avec offres", "shortTerm": "4 semaines", "mediumTerm": "6 mois", "longTerm": "12 mois", "removeFilter": "Supprimer ce filtre"}, "availability": {"title": "Disponibilité", "label": "Uniquement avec offres", "enabledDescription": "Seules les recommandations avec offres d'achat sont affichées", "disabledDescription": "Toutes les recommandations sont affichées, même sans offres d'achat"}}, "refresh": {"button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "refreshing": "Rafraîchissement...", "refreshed": "<PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON>", "errorTitle": "<PERSON><PERSON><PERSON> de rafraîchissement", "errorDescription": "Impossible de lancer la génération de nouvelles recommandations."}, "sharing": {"share": "Partager", "shareList": "Partager votre liste", "shareDescription": "Partagez vos découvertes musicales avec vos amis", "publicSharing": "Partage public", "publicSharingDescription": "Permet à quiconque ayant le lien de voir votre liste", "shareLink": "Lien de partage", "visibilityOptions": "Options de visibilité", "recommendations": "Mes recommandations", "wishlist": "Mes envies", "collection": "Ma collection"}, "wishlist": {"added": "💖 \"{albumTitle}\" de {artist<PERSON>ame} a été ajouté à vos envies !", "addedDescription": "Vous pouvez le retrouver dans votre liste d'envies.", "viewWishlist": "Voir mes envies", "addError": "Erreur lors de l'ajout à la liste d'envies", "addErrorUnexpected": "❌ Impossible d'ajouter l'album à vos envies", "addErrorDescription": "Une erreur inattendue s'est produite. Veuillez réessayer.", "removed": "✅ \"{albumTitle}\" de {artist<PERSON>ame} a été retiré de vos envies.", "removedDescription": "L'album n'apparaît plus dans votre liste d'envies.", "undo": "Annuler", "undoSuccess": "💖 \"{albumTitle}\" a été remis dans vos envies !", "removeError": "Erreur lors de la suppression de la liste d'envies", "removeErrorUnexpected": "❌ Impossible de retirer l'album de vos envies", "removeErrorDescription": "Une erreur inattendue s'est produite. Veuillez réessayer."}, "offers": {"otherOffers": "{count} autre{count, plural, =1 {} other {s}} offre{count, plural, =1 {} other {s}}", "topTrack": "Votre titre phare :", "topTrackForUser": "Titre phare de {userName}", "buy": "<PERSON><PERSON><PERSON>", "wishlist": {"added": "💖 \"{albumTitle}\" de {artist<PERSON>ame} a été ajouté à vos envies !", "addedDescription": "Vous pouvez le retrouver dans votre liste d'envies.", "viewWishlist": "Voir mes envies", "addError": "Erreur lors de l'ajout à la liste d'envies", "addErrorUnexpected": "❌ Impossible d'ajouter l'album à vos envies", "addErrorDescription": "Une erreur inattendue s'est produite. Veuillez réessayer.", "removed": "✅ \"{albumTitle}\" de {artist<PERSON>ame} a été retiré de vos envies.", "removedDescription": "L'album n'apparaît plus dans votre liste d'envies.", "undo": "Annuler", "undoSuccess": "💖 \"{albumTitle}\" a été remis dans vos envies !", "removeError": "Erreur lors de la suppression de la liste d'envies", "removeErrorUnexpected": "❌ Impossible de retirer l'album de vos envies", "removeErrorDescription": "Une erreur inattendue s'est produite. Veuillez réessayer."}}, "timeframes": {"shortTermDescription": "4 dernières semaines", "mediumTermDescription": "6 derniers mois", "longTermDescription": "12 derniers mois", "noRecommendationsFor": "Aucune recommandation pour"}, "share": {"button": "Partager", "title": "Partager votre liste", "description": "Partagez vos découvertes musicales avec vos amis", "publicEnabled": "Partage public activé", "publicDisabled": "Partage public désactivé", "linkCopied": "Le lien a bien été copié dans le presse-papier", "linkCopiedDescription": "Le lien de votre liste a été copié dans le presse-papiers.", "copyPrompt": "Copiez ce lien pour partager votre liste:", "copyError": "Erreur lors de la copie du lien", "copyErrorDescription": "Impossible de copier le lien. Veuillez réessayer.", "updateError": "<PERSON><PERSON>ur lors de la mise à jour", "instagramCopied": "Lien copié pour Instagram", "publicSharing": "Partage public", "publicSharingDescription": "Permet à quiconque ayant le lien de voir votre liste", "shareLink": "Lien de partage", "visibilityOptions": "Options de visibilité"}}, "search": {"placeholder": "Rechercher un album ou un artiste...", "button": "<PERSON><PERSON><PERSON>", "noResults": "Aucun résultat trouvé", "description": "Recherchez manuellement des albums et artistes pour obtenir les meilleurs prix", "comingSoon": "Recherche bientôt disponible!"}, "wishlist": {"title": "Mes envies", "subtitle": "{count, plural, =0 {Aucun album} =1 {1 album} other {# albums}} dans votre liste d'envies", "empty": {"title": "Votre liste d'envies est vide", "description": "Parcourez vos recommandations et cliquez sur le cœur des albums qui vous intéressent pour les retrouver ici.", "cta": "Voir mes recommandations", "tip": {"title": "Astuce", "description": "Cliquez sur l'icône cœur sur n'importe quelle recommandation pour l'ajouter à votre liste d'envies."}}, "removeDialog": {"title": "Retirer de vos envies ?", "description": "Voulez-vous vraiment retirer \"{albumTitle}\" de {artist<PERSON>ame} de votre liste d'envies ?", "cancel": "Annuler", "confirm": "<PERSON><PERSON><PERSON>", "removing": "Suppression..."}, "filters": {"search": "Rechercher dans vos envies...", "sortBy": "Trier par", "sortOptions": {"createdAt_desc": "Date d'ajout (Plus récent)", "createdAt_asc": "Date d'ajout (Plus ancien)", "artistName_asc": "Artiste (A-Z)", "artistName_desc": "Artiste (Z-A)", "albumTitle_asc": "Album (A-Z)", "albumTitle_desc": "Album (Z-A)", "price_asc": "Prix (Croissant)", "price_desc": "Prix (Décroissant)", "year_desc": "Année (Plus récent)", "year_asc": "Ann<PERSON> (Plus ancien)", "syncedAt_desc": "Ajouté (Plus récent)", "syncedAt_asc": "Ajouté (Plus ancien)"}, "results": "{count, plural, =0 {Aucun résultat} =1 {1 résultat} other {# résultats}} pour \"{query}\"", "noResults": "Aucun résultat trouvé", "noResultsDescription": "Aucun album dans votre liste d'envies ne correspond à \"{query}\". Essayez avec d'autres mots-clés.", "albumCount": "{count, plural, =0 {Aucun album} =1 {1 album} other {# albums}}"}}, "collection": {"title": "Ma Collection", "subtitle": "Votre collection de vinyles synchronisée depuis Discogs", "connectPrompt": {"title": "Connectez votre collection Discogs", "description": "Liez votre compte Discogs pour visualiser ici toute votre collection et, surtout, pour que nous ne vous recommandions plus jamais un vinyle que vous possédez déjà.", "button": "Connecter mon compte Discogs", "connecting": "Connexion en cours...", "whyConnect": {"title": "Pourquoi connecter Discogs ?", "description": "Nous éviterons de vous recommander des vinyles que vous possédez déjà et vous pourrez consulter votre collection complète."}}, "empty": {"title": "Votre collection est vide", "description": "Votre collection Discogs ne contient aucun vinyle pour le moment.", "cta": "Découvrir des vinyles", "tip": {"title": "Astuce", "description": "Ajoutez des vinyles à votre collection Discogs pour les voir apparaître ici automatiquement."}}, "loading": "Chargement de votre collection...", "error": "Erreur lors du chargement de votre collection", "filters": {"search": "Rechercher dans votre collection...", "sortBy": "Trier par", "sortOptions": {"artistName_asc": "Artiste (A-Z)", "artistName_desc": "Artiste (Z-A)", "albumTitle_asc": "Album (A-Z)", "albumTitle_desc": "Album (Z-A)", "year_desc": "Année (Plus récent)", "year_asc": "Ann<PERSON> (Plus ancien)", "syncedAt_desc": "Ajouté (Plus récent)", "syncedAt_asc": "Ajouté (Plus ancien)"}, "albumCount": "{count, plural, =0 {Aucun album} =1 {1 album} other {# albums}}"}, "sync": {"button": "Synchroniser", "syncing": "Synchronisation...", "synced": "Synchronisé", "error": "<PERSON><PERSON><PERSON>", "successTitle": "Synchronisation réussie", "successDescription": "{count} album{count, plural, =1 {} other {s}} synchronisé{count, plural, =1 {} other {s}}. Les recommandations ont été mises à jour.", "errorTitle": "Erreur de synchronisation", "errorDescription": "Une erreur est survenue lors de la synchronisation.", "connectionError": "Impossible de se connecter au serveur."}}, "messages": {"discogsConnected": "Compte Discogs connecté avec succès", "discogsDisconnected": "Compte Discogs déconnecté avec succès", "discogsDisconnectError": "Erreur lors de la déconnexion", "preferencesUpdated": "Préférences enregistrées", "languageUpdated": "<PERSON>ue mise à jour"}, "public": {"meta": {"title": "Découvrez la sélection de vinyles de {firstName} sur Stream2Spin !", "description": "Plongez dans les albums que j'écoute en boucle. Une sélection 100% personnelle basée sur mes écoutes Spotify.", "fallbackUser": "Un mélomane", "keywords": "recommandations musicales,albums vinyles,découverte musicale,Spotify,Stream2Spin", "ogImageAlt": "Sélection de vinyles de {firstName} sur Stream2Spin"}, "tabs": {"recommendations": "Ses top écoutes", "wishlist": "Ses envies d'achat", "collection": "Sa collection"}, "profile": {"follow": "Suivre", "followSoon": "(Bientôt disponible)"}, "recommendations": {"title": "Ses top écoutes", "filterDescription": "Filtrez ses top écoutes selon la période de votre choix", "filters": {"title": "Filtres", "show": "<PERSON><PERSON><PERSON><PERSON>", "hide": "Masquer", "timeframe": "Filtrez ses top écoutes selon la période de votre choix", "owned": "<PERSON>yle<PERSON> poss<PERSON>", "showOwned": "Afficher les albums déjà présents dans la collection", "hideOwned": "Masquer les possédés", "availability": "Disponibilité", "availabilityLabel": "Uniquement avec offres", "availabilityEnabledDescription": "Affiche uniquement les albums avec des offres d'achat disponibles", "availabilityDisabledDescription": "Affiche tous les albums, avec ou sans offres d'achat"}, "empty": {"title": "Aucune recommandation pour cette période", "description": "{userName} n'a pas encore de recommandations pour cette période d'écoute."}, "collectionTag": "Dans sa collection"}, "wishlist": {"title": "Ses envies d'achat", "search": "Rechercher dans ses envies...", "empty": {"title": "<PERSON><PERSON>ne en<PERSON>'a<PERSON>", "description": "{userName} n'a pas encore d'albums dans sa liste d'envies."}, "noResults": {"title": "Aucun résultat", "description": "Aucun album ne correspond à votre recherche \"{query}\".", "clear": "Effacer la recherche"}, "addedOn": "<PERSON><PERSON><PERSON> le {date}", "buy": "<PERSON><PERSON><PERSON>", "collectionTag": "Dans sa collection"}, "collection": {"title": "Sa collection", "search": "Rechercher dans sa collection", "empty": {"title": "Collection vide", "description": "{userName} n'a pas encore d'albums dans sa collection."}, "noResults": {"title": "Aucun résultat", "description": "Aucun album ne correspond à votre recherche \"{query}\".", "clear": "Effacer la recherche"}, "buy": "<PERSON><PERSON><PERSON>"}, "wishlistButton": {"loginToAdd": "Se connecter pour ajouter à mes envies", "removeFromWishlist": "<PERSON><PERSON><PERSON> de mes envies", "addToWishlist": "Ajouter à mes envies"}, "notFound": {"title": "Liste introuvable", "description": "Cette liste de recommandations n'existe pas ou n'est plus publique. Elle a peut-être été rendue privée par son propriétaire.", "createList": "<PERSON><PERSON><PERSON> ma liste", "backHome": "Retour à l'accueil"}, "header": {"discover": "Découvrir Stream2Spin", "login": "Se connecter"}}, "errors": {"notFound": {"title": "Page introuvable", "description": "Oups ! Il semble que le disque que vous cherchez ait sauté. Cette page n'existe pas."}, "dataFetchError": "Erreur lors de la récupération des données publiques:"}, "emails": {"welcome": {"subject": "Bienvenue sur Stream2Spin !", "pageTitle": "Bienvenue sur Stream2Spin !", "preview": "Bienvenue sur Stream2Spin ! Voici comment bien démarrer.", "title": "Bienvenue sur Stream2Spin !", "greeting": "Bon<PERSON>r {name},", "intro": "Votre compte Stream2Spin a bien été créé. Préparez-vous à transformer vos écoutes en une collection de vinyles sur mesure.", "adviceTitle": "Nos 2 conseils pour bien démarrer", "advice1_title": "<PERSON><PERSON><PERSON>z les doublons, synchronisez votre collection.", "advice1_text": "Pour que nous ne recommandions que des albums que vous ne possédez pas déjà, connectez votre compte Discogs. C'est rapide et améliore considérablement nos suggestions.", "advice1_cta": "Connecter mon compte Discogs", "advice2_title": "Gardez une trace de vos envies.", "advice2_text": "Un album vous plaît mais vous n'êtes pas prêt à l'acheter ? Ajoutez-le à votre wishlist en cliquant sur le cœur. Vous pourrez le retrouver facilement plus tard.", "advice2_cta": "A<PERSON>ter à ma wishlist", "main_cta": "Explorer mes premières recommandations", "textContent": "Bonjour {name}, votre compte Stream2Spin a bien été créé ! Découvrez nos recommandations personnalisées : {baseUrl}/recommendations", "footer_sent_to": "<PERSON>t email a été envoyé à {userEmail} car vous venez de créer un compte Stream2Spin.", "footer_visit": "Visiter Stream2Spin", "footer_copyright": "© {year} Stream2Spin. Tous droits réservés."}, "newFollower": {"subject": "{name} vous suit maintenant sur Stream2Spin !", "preview": "{name} suit maintenant vos recommandations musicales", "greeting": "Bonjour !", "body": "{name} a commencé à vous suivre sur Stream2Spin et peut maintenant voir vos dernières recommandations musicales dans son feed social.", "cta": "Voir son profil", "footer_info": "Vous recevez cet email car quelqu'un a commencé à vous suivre sur Stream2Spin.", "footer_manage": "<PERSON><PERSON><PERSON> les préférences de notification", "footer_copyright": "© {year} Stream2Spin. Tous droits réservés."}, "recommendations": {"subject": "{totalCount} nouvelles recommandations vinyles pour vous", "preview": "{totalCount} nouvelles recommandations vinyles basées sur vos écoutes Spotify", "greeting": "Bonjour {name} !", "intro": "Nous avons trouvé {totalCount} nouveaux vinyles qui correspondent à vos goûts musicaux :", "personal_title": "Vos recommandations", "community_title": "La communauté écoute :", "recommended_by": "Recommandé par {userName}", "listen_score": "Score d'écoute", "buy_button": "<PERSON><PERSON><PERSON>", "view_all_button": "Voir toutes les recommandations", "footer_text": "Ces recommandations sont basées sur votre activité d'écoute Spotify récente et celle de votre communauté.", "textContent": "Bonjour {name}, vous avez {totalCount} nouvelles recommandations ! Consultez-les ici : {viewRecommendationsUrl}. Pour vous désabonner : {unsubscribeUrl}", "view_all": "Voir Toutes les Recommandations", "buy_on": "Acheter sur", "add_wishlist": "A<PERSON>ter à ma wishlist", "more_to_discover": "Et bien plus encore !", "footer_info": "Ces recommandations sont basées sur votre activité d'écoute Spotify récente.", "unsubscribe": "<PERSON><PERSON><PERSON> les préférences de notification", "powered_by": "Propulsé par Stream2Spin", "company_info": "Stream2Spin, France"}}, "generating": {"initializing": "Initialisation...", "steps": {"analyzing": "Analyse de vos écoutes", "collection": "Synchronisation collection", "offers": "Recherche des offres", "finalizing": "Finalisation"}, "counters": {"tracks_processed": "<PERSON>it<PERSON> trait<PERSON>", "albums_analyzed": "Albums analysés", "offers_found": "Offres trouvées", "recommendations_generated": "Recommandations générées"}, "messages": {"connectionEstablished": "Connexion établie, démarrage de la génération...", "analyzingListening": "Analyse de vos habitudes d'écoute...", "cleanupCompleted": "Nettoyage des anciennes recommandations terminé", "likeArtist": "On dirait que vous aimez beaucoup {artist} en ce moment...", "likeAlbum": "Vous aimez *{album}* de *{artist}*", "youLike": "<PERSON>ous aimez", "byArtist": "de", "unknownAlbum": "un album", "analysisCompleted": "Analyse terminée : {count} albums analysés ✓", "collectionSynced": "Collection Discogs synchronisée ✓", "searchingOffers": "Recherche des meilleures offres...", "amazonLinksGenerated": "Liens Amazon générés ✓", "finalizingRecommendations": "Finalisation de vos recommandations...", "recommendationsReady": "{count} recommandations personnalisées prêtes ✓", "foundPerfectAlbums": "Nous avons trouvé {count} albums parfaits pour vous !", "yourRecommendationsAreReady": "Vos recommandations sont prêtes", "connectionError": "Erreur de connexion. Veuillez rafraîchir la page.", "genericError": "Une erreur est survenue", "noRecommendationsGenerated": "Aucune recommandation générée"}}, "terms": {"title": "Conditions Générales de Service", "introduction": {"title": "1. Introduction", "content": "Bienvenue sur Stream2Spin. En utilisant notre service, vous acceptez de respecter les présentes Conditions Générales de Service. Veuillez les lire attentivement."}, "service": {"title": "2. Des<PERSON> du Service", "content": "Stream2Spin est un service qui analyse vos habitudes d'écoute sur Spotify pour vous recommander des vinyles qui correspondent à vos goûts musicaux."}, "account": {"title": "3. <PERSON><PERSON><PERSON>", "content": "Pour utiliser Stream2Spin, vous devez créer un compte et vous connecter à votre compte Spotify. Vous êtes responsable de maintenir la confidentialité de vos identifiants et de toutes les activités qui se produisent sous votre compte."}, "dataUsage": {"title": "4. Utilisation des Données", "content": "Nous collectons et utilisons vos données conformément à notre Politique de Confidentialité. En utilisant notre service, vous consentez à cette collecte et utilisation."}, "intellectualProperty": {"title": "5. Propri<PERSON>té Intellectuelle", "content": "Tout le contenu disponible sur Stream2Spin, y compris mais sans s'y limiter, les textes, graphiques, logos, icônes, images, clips audio, téléchargements numériques et compilations de données, est la propriété de Stream2Spin ou de ses fournisseurs de contenu et est protégé par les lois internationales sur le droit d'auteur."}, "liability": {"title": "6. Limitation de Responsabilité", "content": "Stream2Spin ne garantit pas l'exactitude, la pertinence ou l'exhaustivité des informations et des recommandations fournies par le service. En aucun cas, Stream2Spin ne sera responsable des dommages directs, indirects, accessoires, spéciaux ou consécutifs résultant de l'utilisation ou de l'impossibilité d'utiliser le service."}, "modifications": {"title": "7. Modifications des Conditions", "content": "Nous nous réservons le droit de modifier ces conditions à tout moment. Les modifications entreront en vigueur dès leur publication sur le site. Il est de votre responsabilité de consulter régulièrement ces conditions."}, "termination": {"title": "8. <PERSON><PERSON><PERSON><PERSON>", "content": "Nous nous réservons le droit de résilier ou de restreindre votre accès à notre service, sans préavis, pour quelque raison que ce soit, y compris, sans limitation, si nous déterminons que vous avez violé ces Conditions."}, "law": {"title": "9. <PERSON>i Applicable", "content": "Ces Conditions sont régies par les lois françaises. Tout litige relatif à ces Conditions sera soumis à la compétence exclusive des tribunaux français."}}, "privacy": {"title": "Politique de Confidentialité", "introduction": {"title": "1. Introduction", "content": "Chez Stream2Spin, nous accordons une grande importance à la protection de vos données personnelles. Cette Politique de Confidentialité explique comment nous collectons, utilisons et protégeons vos informations lorsque vous utilisez notre service."}, "dataCollection": {"title": "2. Informations que nous collectons", "intro": "Nous collectons les types d'informations suivants :", "items": ["Informations de compte : email, nom d'utilisateur, mot de passe hashé.", "Informations de profil Spotify : historique d'écoute, artistes et titres préférés.", "Informations de profil Discogs (si connecté) : collection de vinyles.", "Données d'utilisation : interactions avec l'application, préférences de notification."]}, "dataUsage": {"title": "3. Comment nous utilisons vos informations", "intro": "Nous utilisons vos informations pour :", "items": ["Fournir, maintenir et améliorer notre service.", "Générer des recommandations personnalisées de vinyles.", "Vous envoyer des notifications selon vos préférences.", "Analyser l'utilisation de notre service pour l'améliorer."]}, "dataSharing": {"title": "4. Partage des informations", "intro": "Nous ne vendons pas vos données personnelles à des tiers. Nous pouvons partager vos informations dans les circonstances suivantes :", "items": ["Avec des fournisseurs de services qui nous aident à exploiter notre service.", "Si nécessaire pour se conformer à la loi ou protéger nos droits.", "En cas de fusion, vente ou transfert d'actifs, vos informations peuvent être transférées à la nouvelle entité."]}, "dataSecurity": {"title": "5. <PERSON><PERSON><PERSON><PERSON><PERSON> donn<PERSON>", "content": "Nous mettons en œuvre des mesures de sécurité appropriées pour protéger vos informations contre l'accès, l'altération, la divulgation ou la destruction non autorisés."}, "userRights": {"title": "6. <PERSON><PERSON> d<PERSON>", "content": "Vous avez le droit d'accéder, de corriger, de supprimer ou de limiter l'utilisation de vos données personnelles. Vous pouvez également vous opposer au traitement de vos données ou demander leur portabilité."}, "dataRetention": {"title": "7. Conservation des données", "content": "Nous conservons vos données aussi longtemps que nécessaire pour fournir notre service ou pour respecter nos obligations légales."}, "policyChanges": {"title": "8. Modifications de cette politique", "content": "Nous pouvons mettre à jour cette Politique de Confidentialité de temps à autre. Nous vous informerons de tout changement significatif par email ou par une notification sur notre site."}, "contact": {"title": "9. <PERSON><PERSON> contacter", "content": "Si vous avez des questions concernant cette Politique de Confidentialité, veuillez nous contacter à <EMAIL>."}}, "login": {"tagline": "Tes favoris Spotify<br />bientôt sur ta platine.", "legalTextPrefix": "En continuant, vous acceptez nos", "legalTextMiddle": "et notre", "legalTextSuffix": ".", "termsLink": "Conditions Générales de Service", "privacyLink": "Politique de Confidentialité", "modal": {"title": "Créez un compte pour sauvegarder vos envies !", "description": "Connectez-vous avec Spotify pour sauvegarder cet album dans votre liste d'envies et découvrir vos propres recommandations personnalisées.", "autoAddNote": "Cet album sera automatiquement ajouté à vos envies après connexion.", "followTitle": "Connectez-vous pour suivre {name} !", "followDescription": "Connectez-vous avec Spotify pour suivre {name} et voir leurs recommandations musicales dans votre feed social.", "followAutoAddNote": "Vous suivrez automatiquement {name} après connexion.", "connectButton": "Se connecter avec Spotify", "connecting": "Connexion...", "continueWith": "Continuer avec Spotify", "maybeLater": "Peut-être plus tard"}}, "Social": {"title": "Découverte Sociale", "description": "Découvrez et suivez d'autres mélomanes, explorez leurs recommandations.", "follow": "Suivre", "unfollow": "Ne plus suivre", "loading": "Chargement...", "followSuccess": "Vous suivez maintenant cet utilisateur", "unfollowSuccess": "Vous ne suivez plus cet utilisateur", "followersLabel": "{count, plural, =0 {abonn<PERSON>} =1 {abonn<PERSON>} other {abonn<PERSON>}}", "followingLabel": "{count, plural, =0 {abonnement} =1 {abonnement} other {abonnements}}", "modalTitle": "Relations de {name}", "followersTab": "Abonnés", "followingTab": "Abonnements", "searchPlaceholder": "Rechercher un utilisateur...", "searchNoResults": "Aucun utilisateur trouvé.", "searching": "Recherche en cours...", "errors": {"generic": "Une erreur est survenue. Veuillez réessayer.", "unauthorized": "Non autorisé. Vous devez être connecté.", "selfFollow": "Vous ne pouvez pas vous suivre vous-même."}, "you": "(vous)", "followers": "Abonnés", "following": "Abonnements", "seeMore": "Voir plus", "followBack": "Suivre en retour", "feed": {"title": "Feed Social", "recommendedBy": "Recommandé par <b>{name}</b>", "loadMore": "Voir plus", "loading": "Chargement...", "emptyState": {"title": "Votre feed est vide", "description": "Commencez par suivre des utilisateurs pour voir leurs recommandations ici."}, "endOfFeed": "Vous avez atteint la fin du feed."}, "suggestions": {"title": "Suggestions pour vous", "justification": {"suggestion": "Suggestion de l'équipe", "graph": "<PERSON><PERSON>i par {name}"}}}}
{"account": {"title": "My Account", "profile": {"title": "Profile", "description": "Information from your Spotify account. This information is read-only.", "username": "Username", "email": "Email address", "profilePicture": "Profile picture", "syncedFromSpotify": "Photo synced from your Spotify account", "notDefined": "Not defined"}, "connectedAccounts": {"title": "Connected Accounts", "description": "Manage your connections to external services to improve your recommendations.", "spotify": {"name": "Spotify", "connected": "Connected", "tooltip": "This is your primary login method and cannot be disconnected."}, "discogs": {"name": "Discogs", "connected": "Connected", "notConnected": "Not connected", "connect": "Connect", "disconnect": "Disconnect", "tooltip": "Avoids recommending vinyl records you already own in your collection.", "description": "Connect your Discogs account so we can identify the vinyl records you already own and avoid duplicate recommendations.", "refreshTooltip": "Resync your Discogs collection for up-to-date recommendations", "disconnectDialog": {"title": "Disconnect your Discogs account?", "description": "This action will remove the link to your Discogs account and all cached collection data. Your future recommendations might include albums you already own.", "cancel": "Cancel", "confirm": "Confirm disconnection", "disconnecting": "Disconnecting..."}}, "syncStatus": {"spotify": "Spotify Sync", "discogs": "Discogs Sync", "lastSync": "Last sync", "refresh": "Refresh", "refreshing": "Syncing...", "refreshDescription": "Resync your Spotify listening history and generate new recommendations", "never": "Never synced", "justNow": "Just now", "minutesAgo": "{minutes} min ago", "hoursAgo": "{hours}h ago", "daysAgo": "{days}d ago", "recommendations": "{count} recommendations"}}, "visibility": {"title": "Visibility & Sharing", "description": "Control who can see your profile and what you share.", "level": {"title": "Profile visibility level", "public": {"label": "Public", "description": "People with the link to your profile can access it, even without being logged in to Stream2Spin."}, "users_only": {"label": "Stream2Spin Users", "description": "Only logged-in users can see your profile."}, "private": {"label": "Private", "description": "Only you can see your profile."}}, "sharing": {"title": "Sharing controls", "recommendations": "Share my recommendations", "wishlist": "Share my wishlist", "collection": "Share my collection"}, "privateWarning": "Your profile is currently private. To share it, please change its visibility to 'Public' or 'Stream2Spin Users'.", "save": "Save Changes", "saving": "Saving...", "updateSuccess": "Visibility settings updated.", "updateError": "Error updating settings."}, "notifications": {"title": "Notification Preferences", "description": "Choose how often you want to receive communications.", "email": "Email Notifications", "push": "Push Notifications", "frequency": {"weekly": "Every week", "biweekly": "Every 2 weeks", "monthly": "Every month", "never": "Never"}, "newFollower": {"label": "When someone follows me", "description": "Receive an email for each new follower."}}, "language": {"title": "Interface Language", "description": "Choose your preferred language for the application.", "label": "Language", "placeholder": "Select a language", "updating": "Updating language..."}, "dangerZone": {"title": "Danger Zone", "description": "Irreversible actions regarding your account. Proceed with caution.", "deleteAccount": {"title": "Delete account", "description": "Permanently delete your account and all your personal data.", "button": "Delete account", "dialog": {"title": "Are you absolutely sure?", "description": "This action is **irreversible**. It will permanently delete your account and all your personal data.", "dataList": ["All your personalized recommendations", "Your wishlist", "Your external account connections (Spotify, Discogs)", "Your synchronization history", "All your preferences and settings"], "confirmText": "To confirm, type \"delete my account\" below:", "placeholder": "delete my account", "cancel": "Cancel", "confirm": "Delete permanently", "deleting": "Deleting..."}}}, "deleteConfirmationLabel": "Confirmation text"}, "Notifications": {"title": "Notifications", "description": "Your latest updates.", "noNotifications": "You have no new notifications.", "newFollower": "{name} is now following you."}, "navigation": {"myAccount": "My account", "logout": "Logout", "user": "User", "recommendations": "Recommendations", "social": "Social", "wishlist": "My wishlist", "collection": "My collection", "toggleMenu": "Toggle navigation menu"}, "auth": {"logout": "Sign out", "loggingOut": "Signing out...", "logoutError": "Error during logout:"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "buy": "Buy", "share": "Share", "searchPlaceholder": "Search coming soon!", "sortBy": "Sort by", "topTrack": "Your top track:"}, "recommendations": {"title": "Your Recommendations", "welcome": "Welcome {name}!", "welcomeWithEmail": "Welcome {email}!", "subtitle": "Here are your personalized vinyl recommendations.", "noRecommendationsYet": "Your personalized vinyl recommendations will appear here.", "analyzing": {"title": "We're analyzing your Spotify profile...", "subtitle": "Your first vinyl recommendations will be ready in a moment!", "status": "Analysis in progress", "steps": {"analyzing": "Analyzing your musical tastes", "searching": "Searching for matching vinyl records", "generating": "Generating recommendations"}, "autoUpdate": "This page will update automatically once the analysis is complete."}, "filters": {"filterList": "Filter list", "show": "Show", "hide": "<PERSON>de", "noSpotify": {"message": "🎵 Connect your Spotify account to start receiving recommendations"}, "timeframe": {"title": "Listening period", "shortTerm": "Last 4 weeks", "mediumTerm": "Last 6 months", "longTerm": "Last 12 months", "description": "Filter your recommendations based on your Spotify listening analysis period"}, "owned": {"title": "Owned vinyl", "connectDiscogs": "Connect Discogs", "connectDescription": "Connect your Discogs account to hide vinyl records you already own", "hideOwned": "Hide albums already in collection", "showOwned": "Show owned", "hidingOwnedDescription": "Vinyl records from your Discogs collection are hidden", "showingOwnedDescription": "Vinyl records from your Discogs collection are displayed with a badge"}, "activeTags": {"inCollection": "In collection", "withOffers": "With offers", "shortTerm": "4 weeks", "mediumTerm": "6 months", "longTerm": "12 months", "removeFilter": "Remove this filter"}, "availability": {"title": "Availability", "label": "Only with offers", "enabledDescription": "Only recommendations with purchase offers are displayed", "disabledDescription": "All recommendations are displayed, even without purchase offers"}}, "refresh": {"button": "Refresh", "refreshing": "Refreshing...", "refreshed": "Refreshed", "error": "Error", "errorTitle": "Refresh error", "errorDescription": "Unable to start generating new recommendations."}, "sharing": {"share": "Share", "shareList": "Share your list", "shareDescription": "Share your musical discoveries with your friends", "publicSharing": "Public sharing", "publicSharingDescription": "Allows anyone with the link to see your list", "shareLink": "Share link", "visibilityOptions": "Visibility options", "recommendations": "My recommendations", "wishlist": "My wishlist", "collection": "My collection"}, "wishlist": {"added": "💖 \"{albumTitle}\" by {artist<PERSON><PERSON>} has been added to your wishlist!", "addedDescription": "You can find it in your wishlist.", "viewWishlist": "View my wishlist", "addError": "Error adding to wishlist", "addErrorUnexpected": "❌ Unable to add album to your wishlist", "addErrorDescription": "An unexpected error occurred. Please try again.", "removed": "✅ \"{albumTitle}\" by {artist<PERSON><PERSON>} has been removed from your wishlist.", "removedDescription": "The album no longer appears in your wishlist.", "undo": "Undo", "undoSuccess": "💖 \"{albumTitle}\" has been added back to your wishlist!", "removeError": "Error removing from wishlist", "removeErrorUnexpected": "❌ Unable to remove album from your wishlist", "removeErrorDescription": "An unexpected error occurred. Please try again."}, "offers": {"otherOffers": "{count} other offer{count, plural, =1 {} other {s}}", "topTrack": "Your top track:", "topTrackForUser": "Top track from {userName}", "buy": "Buy", "wishlist": {"added": "💖 \"{albumTitle}\" by {artist<PERSON><PERSON>} has been added to your wishlist!", "addedDescription": "You can find it in your wishlist.", "viewWishlist": "View my wishlist", "addError": "Error adding to wishlist", "addErrorUnexpected": "❌ Unable to add album to your wishlist", "addErrorDescription": "An unexpected error occurred. Please try again.", "removed": "✅ \"{albumTitle}\" by {artist<PERSON><PERSON>} has been removed from your wishlist.", "removedDescription": "The album no longer appears in your wishlist.", "undo": "Undo", "undoSuccess": "💖 \"{albumTitle}\" has been added back to your wishlist!", "removeError": "Error removing from wishlist", "removeErrorUnexpected": "❌ Unable to remove album from your wishlist", "removeErrorDescription": "An unexpected error occurred. Please try again."}}, "timeframes": {"shortTermDescription": "Last 4 weeks", "mediumTermDescription": "Last 6 months", "longTermDescription": "Last 12 months", "noRecommendationsFor": "No recommendations for"}, "share": {"button": "Share", "title": "Share your list", "description": "Share your musical discoveries with your friends", "publicEnabled": "Public sharing enabled", "publicDisabled": "Public sharing disabled", "linkCopied": "Link copied!", "linkCopiedDescription": "Your list link has been copied to clipboard.", "copyPrompt": "Copy this link to share your list:", "copyError": "Error copying link", "copyErrorDescription": "Unable to copy link. Please try again.", "updateError": "Error updating settings", "instagramCopied": "Link copied for Instagram", "publicSharing": "Public sharing", "publicSharingDescription": "Allows anyone with the link to see your list", "shareLink": "Share link", "visibilityOptions": "Visibility options"}}, "search": {"placeholder": "Search for an album or artist...", "button": "Search", "noResults": "No results found", "description": "Manually search for albums and artists to get the best prices", "comingSoon": "Search coming soon!"}, "wishlist": {"title": "My wishlist", "subtitle": "{count, plural, =0 {No albums} =1 {1 album} other {# albums}} in your wishlist", "empty": {"title": "Your wishlist is empty", "description": "Browse your recommendations and click the heart icon on albums that interest you to find them here.", "cta": "View my recommendations", "tip": {"title": "Tip", "description": "Click the heart icon on any recommendation to add it to your wishlist."}}, "removeDialog": {"title": "Remove from wishlist?", "description": "Are you sure you want to remove \"{albumTitle}\" by {artist<PERSON><PERSON>} from your wishlist?", "cancel": "Cancel", "confirm": "Remove", "removing": "Removing..."}, "filters": {"search": "Search in your wishlist...", "sortBy": "Sort by", "sortOptions": {"createdAt_desc": "Date added (Newest first)", "createdAt_asc": "Date added (Oldest first)", "artistName_asc": "Artist (A-Z)", "artistName_desc": "Artist (Z-A)", "albumTitle_asc": "Album (A-Z)", "albumTitle_desc": "Album (Z-A)", "price_asc": "Price (Low to High)", "price_desc": "Price (High to Low)", "year_desc": "Year (Newest)", "year_asc": "Year (Oldest)", "syncedAt_desc": "Added (Newest)", "syncedAt_asc": "Added (Oldest)"}, "results": "{count, plural, =0 {No results} =1 {1 result} other {# results}} for \"{query}\"", "noResults": "No results found", "noResultsDescription": "No albums in your wishlist match \"{query}\". Try different keywords.", "albumCount": "{count, plural, =0 {No albums} =1 {1 album} other {# albums}}"}}, "collection": {"title": "My Collection", "subtitle": "Your vinyl collection synced from Discogs", "connectPrompt": {"title": "Connect your Discogs collection", "description": "Link your Discogs account to view your entire collection here and, most importantly, so we never recommend a vinyl record you already own.", "button": "Connect my Discogs account", "connecting": "Connecting...", "whyConnect": {"title": "Why connect Discogs?", "description": "We'll avoid recommending vinyl records you already own and you'll be able to view your complete collection."}}, "empty": {"title": "Your collection is empty", "description": "Your Discogs collection doesn't contain any vinyl records at the moment.", "cta": "Discover vinyl records", "tip": {"title": "Tip", "description": "Add vinyl records to your Discogs collection to see them appear here automatically."}}, "loading": "Loading your collection...", "error": "Error loading your collection", "filters": {"search": "Search in your collection...", "sortBy": "Sort by", "sortOptions": {"artistName_asc": "Artist (A-Z)", "artistName_desc": "Artist (Z-A)", "albumTitle_asc": "Album (A-Z)", "albumTitle_desc": "Album (Z-A)", "year_desc": "Year (Newest)", "year_asc": "Year (Oldest)", "syncedAt_desc": "Added (Newest)", "syncedAt_asc": "Added (Oldest)"}, "albumCount": "{count, plural, =0 {No albums} =1 {1 album} other {# albums}}"}, "sync": {"button": "Sync", "syncing": "Syncing...", "synced": "Synced", "error": "Error", "successTitle": "Sync successful", "successDescription": "{count} album{count, plural, =1 {} other {s}} synced. Recommendations have been updated.", "errorTitle": "Sync error", "errorDescription": "An error occurred during synchronization.", "connectionError": "Unable to connect to server."}}, "messages": {"discogsConnected": "Discogs account connected successfully", "discogsDisconnected": "Discogs account disconnected successfully", "discogsDisconnectError": "Error during disconnection", "preferencesUpdated": "Preferences saved", "languageUpdated": "Language updated"}, "public": {"meta": {"title": "Discover {firstName}'s vinyl selection on Stream2Spin!", "description": "Dive into the albums I listen to on repeat. A 100% personal selection based on my Spotify listening habits.", "fallbackUser": "A music lover", "keywords": "music recommendations,vinyl albums,music discovery,Spotify,Stream2Spin", "ogImageAlt": "{firstName}'s vinyl selection on Stream2Spin"}, "tabs": {"recommendations": "Their top listens", "wishlist": "Their wishlist", "collection": "Their collection"}, "profile": {"follow": "Follow", "followSoon": "(Coming soon)"}, "recommendations": {"title": "Their top listens", "filterDescription": "Filter their top listens by time period of your choice", "filters": {"title": "Filters", "show": "Show", "hide": "<PERSON>de", "timeframe": "Filter their top listens by time period of your choice", "owned": "Owned vinyl records", "showOwned": "Show albums already in collection", "hideOwned": "Hide owned", "availability": "Availability", "availabilityLabel": "Only with offers", "availabilityEnabledDescription": "Shows only albums with available purchase offers", "availabilityDisabledDescription": "Shows all albums, with or without purchase offers"}, "empty": {"title": "No recommendations for this period", "description": "{userName} doesn't have recommendations for this listening period yet."}, "collectionTag": "In their collection"}, "wishlist": {"title": "Their wishlist", "search": "Search in their wishlist...", "empty": {"title": "No wishlist items", "description": "{user<PERSON>ame} doesn't have any albums in their wishlist yet."}, "noResults": {"title": "No results", "description": "No albums match your search \"{query}\".", "clear": "Clear search"}, "addedOn": "Added on {date}", "buy": "Buy", "collectionTag": "In their collection"}, "collection": {"title": "Their collection", "search": "Search in their collection", "empty": {"title": "Empty collection", "description": "{userName} doesn't have any albums in their collection yet."}, "noResults": {"title": "No results", "description": "No albums match your search \"{query}\".", "clear": "Clear search"}, "buy": "Buy"}, "wishlistButton": {"loginToAdd": "Sign in to add to wishlist", "removeFromWishlist": "Remove from wishlist", "addToWishlist": "Add to wishlist"}, "notFound": {"title": "List not found", "description": "This recommendation list doesn't exist or is no longer public. It may have been made private by its owner.", "createList": "Create my list", "backHome": "Back to home"}, "header": {"discover": "Discover Stream2Spin", "login": "Sign in"}}, "generating": {"initializing": "Initializing...", "steps": {"analyzing": "Analyzing your listening habits", "collection": "Syncing collection", "offers": "Finding offers", "finalizing": "Finalizing"}, "counters": {"tracks_processed": "Tracks processed", "albums_analyzed": "Albums analyzed", "offers_found": "Offers found", "recommendations_generated": "Recommendations generated"}, "messages": {"connectionEstablished": "Connection established, starting generation...", "analyzingListening": "Analyzing your listening habits...", "cleanupCompleted": "Cleanup of old recommendations completed", "likeArtist": "It looks like you really like {artist} right now...", "likeAlbum": "You like *{album}* by *{artist}*", "youLike": "You like", "byArtist": "by", "unknownAlbum": "an album", "analysisCompleted": "Analysis completed: {count} albums analyzed ✓", "collectionSynced": "Discogs collection synced ✓", "searchingOffers": "Searching for the best offers...", "amazonLinksGenerated": "Amazon links generated ✓", "finalizingRecommendations": "Finalizing your recommendations...", "recommendationsReady": "{count} personalized recommendations ready ✓", "foundPerfectAlbums": "We found {count} perfect albums for you!", "yourRecommendationsAreReady": "Your recommendations are ready", "connectionError": "Connection error. Please refresh the page.", "genericError": "An error occurred", "noRecommendationsGenerated": "No recommendations generated"}}, "terms": {"title": "Terms of Service", "introduction": {"title": "1. Introduction", "content": "Welcome to Stream2Spin. By using our service, you agree to comply with these Terms of Service. Please read them carefully."}, "service": {"title": "2. Service Description", "content": "Stream2Spin is a service that analyzes your Spotify listening habits to recommend vinyl records that match your musical tastes."}, "account": {"title": "3. User Account", "content": "To use Stream2Spin, you must create an account and connect to your Spotify account. You are responsible for maintaining the confidentiality of your credentials and all activities that occur under your account."}, "dataUsage": {"title": "4. Data Usage", "content": "We collect and use your data in accordance with our Privacy Policy. By using our service, you consent to this collection and use."}, "intellectualProperty": {"title": "5. Intellectual Property", "content": "All content available on Stream2Spin, including but not limited to texts, graphics, logos, icons, images, audio clips, digital downloads and data compilations, is the property of Stream2Spin or its content providers and is protected by international copyright laws."}, "liability": {"title": "6. Limitation of Liability", "content": "Stream2Spin does not guarantee the accuracy, relevance or completeness of the information and recommendations provided by the service. Under no circumstances shall Stream2Spin be liable for direct, indirect, incidental, special or consequential damages resulting from the use or inability to use the service."}, "modifications": {"title": "7. Modifications to Terms", "content": "We reserve the right to modify these terms at any time. Changes will take effect upon publication on the site. It is your responsibility to regularly review these terms."}, "termination": {"title": "8. Termination", "content": "We reserve the right to terminate or restrict your access to our service, without notice, for any reason, including, without limitation, if we determine that you have violated these Terms."}, "law": {"title": "9. Applicable Law", "content": "These Terms are governed by French law. Any dispute relating to these Terms shall be subject to the exclusive jurisdiction of French courts."}}, "privacy": {"title": "Privacy Policy", "introduction": {"title": "1. Introduction", "content": "At Stream2Spin, we attach great importance to the protection of your personal data. This Privacy Policy explains how we collect, use and protect your information when you use our service."}, "dataCollection": {"title": "2. Information we collect", "intro": "We collect the following types of information:", "items": ["Account information: email, username, hashed password.", "Spotify profile information: listening history, favorite artists and tracks.", "Discogs profile information (if connected): vinyl collection.", "Usage data: interactions with the application, notification preferences."]}, "dataUsage": {"title": "3. How we use your information", "intro": "We use your information to:", "items": ["Provide, maintain and improve our service.", "Generate personalized vinyl recommendations.", "Send you notifications according to your preferences.", "Analyze usage of our service to improve it."]}, "dataSharing": {"title": "4. Information sharing", "intro": "We do not sell your personal data to third parties. We may share your information under the following circumstances:", "items": ["With service providers who help us operate our service.", "If necessary to comply with the law or protect our rights.", "In the event of a merger, sale or transfer of assets, your information may be transferred to the new entity."]}, "dataSecurity": {"title": "5. Data security", "content": "We implement appropriate security measures to protect your information against unauthorized access, alteration, disclosure or destruction."}, "userRights": {"title": "6. Your rights", "content": "You have the right to access, correct, delete or limit the use of your personal data. You can also object to the processing of your data or request its portability."}, "dataRetention": {"title": "7. Data retention", "content": "We retain your data for as long as necessary to provide our service or to comply with our legal obligations."}, "policyChanges": {"title": "8. Changes to this policy", "content": "We may update this Privacy Policy from time to time. We will notify you of any significant changes by email or through a notification on our site."}, "contact": {"title": "9. Contact us", "content": "If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>."}}, "login": {"tagline": "Your Spotify favorites<br />soon on your turntable.", "legalTextPrefix": "By continuing, you accept our", "legalTextMiddle": "and our", "legalTextSuffix": ".", "termsLink": "Terms of Service", "privacyLink": "Privacy Policy", "modal": {"title": "Create an account to save your wishlist!", "description": "Sign in with Spotify to save this album to your wishlist and discover your own personalized recommendations.", "autoAddNote": "This album will be automatically added to your wishlist after signing in.", "followTitle": "Sign in to follow {name}!", "followDescription": "Sign in with Spotify to follow {name} and see their music recommendations in your social feed.", "followAutoAddNote": "You will automatically follow {name} after signing in.", "connectButton": "Sign in with Spotify", "connecting": "Connecting...", "continueWith": "Continue with Spotify", "maybeLater": "Maybe later"}}, "errors": {"notFound": {"title": "Page not found", "description": "Oops! It seems the record you're looking for has skipped. This page doesn't exist."}, "dataFetchError": "Error retrieving public data:"}, "emails": {"welcome": {"subject": "Welcome to Stream2Spin!", "pageTitle": "Welcome to Stream2Spin!", "preview": "Welcome to Stream2Spin! Here's how to get started.", "title": "Welcome to Stream2Spin!", "greeting": "Hello {name},", "intro": "Your Stream2Spin account has been successfully created. Get ready to turn your streams into a custom vinyl collection.", "adviceTitle": "Our 2 tips to get started", "advice1_title": "Avoid duplicates, sync your collection.", "advice1_text": "To ensure we only recommend albums you don't already own, connect your Discogs account. It's quick and drastically improves our suggestions.", "advice1_cta": "Connect my Discogs Account", "advice2_title": "Keep track of your wishes.", "advice2_text": "Like an album but not ready to buy it? Add it to your wishlist by clicking the heart. You can easily find it later.", "advice2_cta": "Add to Wishlist", "main_cta": "Explore My First Recommendations", "textContent": "Hello {name}, your Stream2Spin account has been successfully created! Discover your personalized recommendations: {baseUrl}/recommendations", "footer_sent_to": "This email was sent to {userEmail} because you just created a Stream2Spin account.", "footer_visit": "Visit Stream2Spin", "footer_copyright": "© {year} Stream2Spin. All rights reserved."}, "newFollower": {"subject": "{name} is now following you on Stream2Spin!", "preview": "{name} is now following your music recommendations", "greeting": "Hello!", "body": "{name} started following you on Stream2Spin and can now see your latest music recommendations in their social feed.", "cta": "View their profile", "footer_info": "You're receiving this email because someone started following you on Stream2Spin.", "footer_manage": "Manage notification preferences", "footer_copyright": "© {year} Stream2Spin. All rights reserved."}, "recommendations": {"subject": "{totalCount, plural, =1 {# new vinyl recommendation} other {# new vinyl recommendations}} for you", "preview": "{totalCount, plural, =1 {# new vinyl recommendation} other {# new vinyl recommendations}} based on your listening.", "greeting": "Hello {name}!", "intro": "We've found {totalCount, plural, =1 {# new vinyl} other {# new vinyls}} that match your music taste:", "personal_title": "Your Recommendations", "community_title": "The Community Listens:", "view_all": "View All Recommendations", "buy_on": "Buy on", "add_wishlist": "Add to wishlist", "more_to_discover": "And much more!", "footer_info": "These recommendations are based on your recent Spotify listening activity.", "unsubscribe": "Manage notification preferences", "powered_by": "Powered by Stream2Spin", "company_info": "Stream2Spin, France"}}, "Social": {"title": "Social Discovery", "description": "Discover and follow other music lovers, explore their recommendations.", "follow": "Follow", "unfollow": "Unfollow", "loading": "Loading...", "followSuccess": "You are now following this user", "unfollowSuccess": "You are no longer following this user", "followersLabel": "{count, plural, =0 {follower} =1 {follower} other {followers}}", "followingLabel": "{count, plural, =0 {following} =1 {following} other {following}}", "modalTitle": "Connections for {name}", "followersTab": "Followers", "followingTab": "Following", "searchPlaceholder": "Search for a user...", "searchNoResults": "No users found.", "searching": "Searching...", "errors": {"generic": "An error occurred. Please try again.", "unauthorized": "Unauthorized. You must be logged in.", "selfFollow": "You cannot follow yourself."}, "you": "(you)", "followers": "Followers", "following": "Following", "seeMore": "See more", "followBack": "Follow back", "feed": {"title": "Social Feed", "recommendedBy": "Recommended by <b>{name}</b>", "loadMore": "Load more", "loading": "Loading...", "emptyState": {"title": "Your feed is empty", "description": "Start following users to see their recommendations here."}, "endOfFeed": "You've reached the end of the feed."}, "suggestions": {"title": "Suggestions for you", "justification": {"suggestion": "Team suggestion", "graph": "Followed by {name}"}}}}
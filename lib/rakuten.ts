/**
 * Client API Rakuten Advertising pour l'affiliation
 * Epic 3 - US 3.3: Enrichissement des recommandations avec des offres de prix
 */

export interface RakutenOffer {
  vendor: string;
  url: string;
  price: number;
  currency: string;
  merchantId: string;
  productName: string;
  inStock: boolean;
}

export interface RakutenProduct {
  productname: string;
  price: string;
  currency: string;
  mid: string;
  merchantname: string;
  linkurl: string;
  imageurl?: string;
  upccode?: string;
  sku?: string;
  description?: string;
}

export interface RakutenSearchResponse {
  TotalMatches: string;
  TotalPages: string;
  PageNumber: string;
  item?: RakutenProduct[];
}

/**
 * Configuration des marchands Rakuten partenaires
 * Ces IDs correspondent aux programmes d'affiliation auxquels nous sommes inscrits
 */
const RAKUTEN_MERCHANTS = {
  FNAC: "2625", // ID Fnac sur Rakuten
  CULTURA: "3785", // ID Cultura sur Rakuten  
  AMAZON: "1203", // ID Amazon sur Rakuten
  // Ajouter d'autres marchands selon les programmes disponibles
} as const;

/**
 * Client Rakuten pour rechercher des produits vinyles
 */
export class RakutenClient {
  private readonly baseUrl = "https://api.linksynergy.com";

  constructor() {
    // Vérifier que les credentials OAuth sont configurés
    const clientId = process.env.RAKUTEN_CLIENT_ID;
    const clientSecret = process.env.RAKUTEN_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      throw new Error("Configuration Rakuten manquante: RAKUTEN_CLIENT_ID et RAKUTEN_CLIENT_SECRET requis");
    }
  }

  /**
   * Recherche des produits vinyles pour un album spécifique
   * Effectue des recherches parallèles sur tous les marchands partenaires
   */
  async searchVinylOffers(artistName: string, albumTitle: string): Promise<RakutenOffer[]> {
    try {
      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      console.log(`🔍 Recherche Rakuten pour: "${artistName}" - "${albumTitle}"`);
      }
      }
      }

      // Construire la requête de recherche optimisée
      const searchQuery = this.buildSearchQuery(artistName, albumTitle);
      
      // Rechercher sur tous les marchands en parallèle
      const merchantSearches = Object.entries(RAKUTEN_MERCHANTS).map(([merchantName, merchantId]) =>
        this.searchOnMerchant(searchQuery, merchantId, merchantName)
      );

      const results = await Promise.allSettled(merchantSearches);
      
      // Agréger les résultats valides
      const allOffers: RakutenOffer[] = [];
      let successCount = 0;
      let errorCount = 0;

      results.forEach((result, index) => {
        const merchantName = Object.keys(RAKUTEN_MERCHANTS)[index];
        
        if (result.status === "fulfilled" && result.value) {
          allOffers.push(result.value);
          successCount++;
          if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
          console.log(`✅ ${merchantName}: Offre trouvée à ${result.value.price}${result.value.currency}`);
          }
          }
          }
        } else {
          errorCount++;
          if (process.env.NODE_ENV === 'development') {
          console.log(`❌ ${merchantName}: Aucune offre trouvée`);
          }
          if (result.status === "rejected") {
            console.error(`❌ ${merchantName}: Erreur -`, result.reason);
          }
        }
      });

      // Trier par prix croissant
      allOffers.sort((a, b) => a.price - b.price);

      if (process.env.NODE_ENV === 'development') {
      console.log(`🎯 Recherche Rakuten terminée: ${successCount} offres trouvées, ${errorCount} échecs`);
      }
      
      return allOffers;

    } catch (error) {
      console.error("❌ Erreur lors de la recherche Rakuten:", error);
      return [];
    }
  }

  /**
   * Recherche sur un marchand spécifique
   */
  private async searchOnMerchant(
    searchQuery: string,
    merchantId: string,
    merchantName: string
  ): Promise<RakutenOffer | null> {
    try {
      // Obtenir un token d'accès valide
      const { getRakutenToken } = await import('@/lib/rakuten-auth');
      const token = await getRakutenToken();

      const url = `${this.baseUrl}/productsearch/1.0`;

      const params = new URLSearchParams({
        keyword: searchQuery,
        mid: merchantId, // Merchant ID
        max: "5", // Limiter à 5 résultats pour optimiser
        pagenumber: "1",
        sort: "price", // Trier par prix
        sorttype: "asc", // Prix croissant
      });

      const response = await fetch(`${url}?${params}`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "User-Agent": "Stream2Spin/1.0",
        },
        // Timeout de 10 secondes par marchand
        signal: AbortSignal.timeout(10000),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: RakutenSearchResponse = await response.json();
      
      if (!data.item || data.item.length === 0) {
        return null;
      }

      // Prendre le premier produit (généralement le plus pertinent)
      const product = data.item[0];
      
      // Vérifier que c'est bien un vinyle
      if (!this.isVinylProduct(product)) {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        console.log(`⚠️ ${merchantName}: Produit trouvé mais ne semble pas être un vinyle`);
        }
        }
        }
        return null;
      }

      // Parser le prix
      const price = parseFloat(product.price);
      if (isNaN(price)) {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        console.log(`⚠️ ${merchantName}: Prix invalide: ${product.price}`);
        }
        }
        }
        return null;
      }

      return {
        vendor: merchantName,
        url: product.linkurl,
        price: price,
        currency: product.currency || 'EUR',
        merchantId: merchantId,
        productName: product.productname,
        inStock: true, // Rakuten ne fournit pas toujours cette info, on assume true
      };

    } catch (error) {
      console.error(`❌ Erreur recherche ${merchantName}:`, error);
      return null;
    }
  }

  /**
   * Construit une requête de recherche optimisée pour les vinyles
   */
  private buildSearchQuery(artistName: string, albumTitle: string): string {
    // Nettoyer les noms pour la recherche
    const cleanArtist = this.cleanSearchTerm(artistName);
    const cleanAlbum = this.cleanSearchTerm(albumTitle);
    
    // Construire la requête avec le mot-clé "vinyl" pour cibler les vinyles
    return `${cleanArtist} ${cleanAlbum} vinyl`;
  }

  /**
   * Nettoie un terme de recherche pour optimiser les résultats
   */
  private cleanSearchTerm(term: string): string {
    return term
      // Supprimer les caractères spéciaux
      .replace(/[^\w\s]/g, " ")
      // Supprimer les termes entre parenthèses/crochets
      .replace(/\([^)]*\)/g, "")
      .replace(/\[[^\]]*\]/g, "")
      // Supprimer les mots-clés courants qui peuvent perturber
      .replace(/\b(deluxe|remastered|edition|expanded|special|limited)\b/gi, "")
      // Normaliser les espaces
      .replace(/\s+/g, " ")
      .trim();
  }

  /**
   * Vérifie si un produit est probablement un vinyle
   */
  private isVinylProduct(product: RakutenProduct): boolean {
    const productName = product.productname.toLowerCase();
    const description = product.description?.toLowerCase() || "";
    
    // Mots-clés positifs pour les vinyles
    const vinylKeywords = ["vinyl", "lp", "record", "33 rpm", "180g", "gatefold"];
    const hasVinylKeyword = vinylKeywords.some(keyword => 
      productName.includes(keyword) || description.includes(keyword)
    );
    
    // Mots-clés négatifs (CD, digital, etc.)
    const excludeKeywords = ["cd", "digital", "mp3", "download", "streaming", "cassette"];
    const hasExcludeKeyword = excludeKeywords.some(keyword => 
      productName.includes(keyword) || description.includes(keyword)
    );
    
    return hasVinylKeyword && !hasExcludeKeyword;
  }
}

/**
 * Instance singleton du client Rakuten
 */
let rakutenClient: RakutenClient | null = null;

/**
 * Obtient l'instance du client Rakuten
 */
export function getRakutenClient(): RakutenClient {
  if (!rakutenClient) {
    rakutenClient = new RakutenClient();
  }
  return rakutenClient;
}

/**
 * Fonction utilitaire pour rechercher des offres vinyles
 * Utilisée par le cron job de génération de recommandations
 */
export async function searchVinylOffers(artistName: string, albumTitle: string): Promise<RakutenOffer[]> {
  // Vérifier si Rakuten est activé
  if (process.env.RAKUTEN_ENABLED === 'false') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🚫 Rakuten désactivé via RAKUTEN_ENABLED=false`);
    }
    return [];
  }

  const client = getRakutenClient();
  return client.searchVinylOffers(artistName, albumTitle);
}

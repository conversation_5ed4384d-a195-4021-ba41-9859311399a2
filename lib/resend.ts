/**
 * Resend Email Service
 * Epic 6 - Notifications Utilisateur
 *
 * Ce module gère l'envoi d'emails transactionnels via Resend
 * pour notifier les utilisateurs de leurs nouvelles recommandations.
 */

import { Resend } from 'resend';
import { render } from '@react-email/render';
import { db } from '@/lib/db';
import { accounts } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
// Imports fs supprimés - utilisation d'imports directs pour Vercel
// Imports pour email deliverability supprimés - système simplifié

// Configuration Resend
const resend = new Resend(process.env.RESEND_API_KEY);

// Configuration des emails optimisée pour la délivrabilité
// Utilise le domaine vérifié mails.stream2spin.com pour tous les environnements
const EMAIL_CONFIG = {
  from: process.env.RESEND_FROM_EMAIL || 'Stream2Spin <<EMAIL>>',
  replyTo: process.env.RESEND_REPLY_TO || '<EMAIL>',
  // Headers pour améliorer la délivrabilité
  headers: {
    'X-Entity-Ref-ID': 'stream2spin-notifications',
    'List-Unsubscribe': '<mailto:<EMAIL>>',
    'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
    'Precedence': 'bulk', // Indique que c'est un email en masse
  }
} as const;

// Interface pour les données de recommandation dans l'email
export interface EmailRecommendation {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string;
  listenScore: number;
  affiliateLinks?: Array<{
    vendor: string;
    url: string;
    price?: number;
    currency?: string;
  }>;
  isOwned: boolean;
}

// Interface pour les recommandations de la communauté avec nom d'utilisateur
export interface EmailCommunityRecommendation extends EmailRecommendation {
  userName: string;
}

// Interface pour les données utilisateur dans l'email
export interface EmailUserData {
  id: string;
  name?: string;
  email: string;
  preferredLanguage: string;
}

/**
 * Envoie un email de notification de nouvelles recommandations avec fonctionnalités sociales
 */
export async function sendSocialRecommendationsEmail(
  user: EmailUserData,
  personalRecommendations: EmailRecommendation[],
  communityRecommendations: EmailCommunityRecommendation[] = []
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`📧 Envoi d'email social à ${user.email} (${personalRecommendations.length} perso + ${communityRecommendations.length} communauté)`);
    }

    // Vérifier la configuration
    if (!process.env.RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not configured');
    }

    if (!process.env.NEXTAUTH_URL) {
      throw new Error('NEXTAUTH_URL is not configured');
    }

    // Initialiser Resend
    const resend = new Resend(process.env.RESEND_API_KEY);

    // Créer la fonction de traduction selon la langue de l'utilisateur
    const locale = user.preferredLanguage || 'fr';
    const messages = await import(`@/messages/${locale}.json`);

    const t = (key: string, values?: Record<string, any>) => {
      const keys = key.split('.');
      let value: any = messages.default || messages;

      for (const k of keys) {
        value = value?.[k];
      }

      if (typeof value === 'string' && values) {
        return value.replace(/\{(\w+)\}/g, (match, key) => {
          return values[key]?.toString() || match;
        });
      }

      return value || key;
    };

    // Calculer le total pour le sujet et l'intro
    const totalCount = personalRecommendations.length + communityRecommendations.length;

    // Filtrer les recommandations pour l'affichage (max 10 au total)
    const maxPersonal = Math.min(personalRecommendations.length, 5);
    const maxCommunity = Math.min(communityRecommendations.length, 5);

    const displayPersonalRecommendations = personalRecommendations.slice(0, maxPersonal);
    const displayCommunityRecommendations = communityRecommendations.slice(0, maxCommunity);

    // Importer le template d'email approprié selon la langue
    const { SocialRecommendationsEmailTemplate } = await import('@/emails/social-recommendations-email');

    // Définir les URLs pour l'email
    const unsubscribeUrl = `${process.env.NEXTAUTH_URL}/account?tab=notifications`;
    const viewRecommendationsUrl = `${process.env.NEXTAUTH_URL}/recommendations`;

    // Générer le contenu HTML de l'email
    const emailHtml = await render(
      SocialRecommendationsEmailTemplate({
        user,
        personalRecommendations: displayPersonalRecommendations,
        communityRecommendations: displayCommunityRecommendations,
        totalCount,
        unsubscribeUrl,
        viewRecommendationsUrl,
        t
      })
    );

    // Générer le sujet selon la langue
    const subject = t('emails.recommendations.subject', { totalCount });

    // Générer le contenu texte selon la langue
    const textContent = t('emails.recommendations.textContent', {
      name: user.name || 'Utilisateur',
      totalCount,
      unsubscribeUrl,
      viewRecommendationsUrl
    });

    // Envoyer l'email
    const result = await resend.emails.send({
      from: EMAIL_CONFIG.from,
      to: user.email,
      subject: subject,
      html: emailHtml,
      text: textContent,
      replyTo: EMAIL_CONFIG.replyTo
    });

    if (result.error) {
      if (process.env.NODE_ENV === 'development') {
      console.error('❌ Erreur lors de l\'envoi:', result.error);
      }
      throw new Error(`Erreur Resend: ${result.error.message}`);
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Email social envoyé avec succès (ID: ${result.data?.id})`);
    }

    return {
      success: true,
      messageId: result.data?.id
    };

  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
    console.error('❌ Erreur lors de l\'envoi d\'email social:', error);
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    };
  }
}

/**
 * Envoie un email de notification de nouvelles recommandations (version classique)
 */
export async function sendRecommendationsEmail(
  user: EmailUserData,
  recommendations: EmailRecommendation[]
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`📧 Envoi d'email de recommandations à ${user.email} (${recommendations.length} recommandations brutes)`);
    }

    // Vérifier la configuration
    if (!process.env.RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not configured');
    }

    // Filtrer les recommandations selon les critères avancés
    const filterResult = await filterRecommendationsForEmail(recommendations, user.id);

    if (filterResult.totalCount === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`📭 Aucune recommandation valide après filtrage pour ${user.email}`);
      }
      return { success: false, error: 'No valid recommendations after filtering' };
    }

    // Charger les traductions (import direct pour Vercel)
    const locale = user.preferredLanguage || 'fr';
    let messages: any;
    try {
      if (locale === 'en') {
        const module = await import('@/messages/en.json');
        messages = module.default || module;
      } else {
        const module = await import('@/messages/fr.json');
        messages = module.default || module;
      }
    } catch (error) {
      console.warn(`⚠️ Impossible de charger les traductions pour ${locale}, utilisation du français par défaut`);
      const module = await import('@/messages/fr.json');
      messages = module.default || module;
    }

    // Créer la fonction de traduction avec support des pluriels ICU
    function t(key: string, values: Record<string, any> = {}): string {
      const keys = key.split('.');
      let translation: any = messages;
      
      for (const k of keys) {
        if (translation && typeof translation === 'object') {
          translation = translation[k];
        } else {
          return key;
        }
      }

      if (typeof translation === 'string') {
        let result = translation;
        
        // Gestion des pluriels ICU MessageFormat de façon itérative
        // Traiter les pluriels un par un jusqu'à ce qu'il n'y en ait plus
        let hasPlurals = true;
        while (hasPlurals) {
          // Regex pour détecter les pluriels (avec ou sans =0)
          const pluralRegex = /\{(\w+),\s*plural,\s*(?:=0\s*\{([^}]*)\}\s*)?=1\s*\{([^}]*)\}\s*other\s*\{([^}]*)\}\}/;
          const match = result.match(pluralRegex);
          
          if (match) {
            const [fullMatch, varName, zero, singular, plural] = match;
            const count = values[varName] || 0;
            
            let replacement;
            if (count === 0 && zero !== undefined) {
              replacement = zero;
            } else if (count === 1) {
              replacement = singular;
            } else {
              replacement = plural;
            }
            
            // Remplacer le caractère # par le nombre dans le résultat
            replacement = replacement.replace(/#/g, String(count));
            
            result = result.replace(fullMatch, replacement);
          } else {
            hasPlurals = false;
          }
        }

        // Remplacer les variables standard
        Object.keys(values).forEach(key => {
          const regex = new RegExp(`\\{${key}\\}`, 'g');
          result = result.replace(regex, String(values[key] || ''));
        });

        return result;
      }

      return key;
    }

    // Importer le template d'email approprié selon la langue
    const { RecommendationsEmailTemplate } = await import('@/emails/recommendations-email');

    // Définir les URLs pour l'email
    const unsubscribeUrl = `${process.env.NEXTAUTH_URL}/account?tab=notifications`;
    const viewRecommendationsUrl = `${process.env.NEXTAUTH_URL}/recommendations`;

    // Générer le contenu HTML de l'email
    const emailHtml = await render(
      RecommendationsEmailTemplate({
        user,
        recommendations: filterResult.recommendations,
        totalCount: filterResult.totalCount,
        unsubscribeUrl,
        viewRecommendationsUrl,
        t
      })
    );

    // Générer le sujet selon la langue
    const subject = t('emails.recommendations.subject', { totalCount: filterResult.totalCount });

    // Générer le contenu texte selon la langue
    const textContent = t('emails.recommendations.textContent', {
      name: user.name || 'Utilisateur',
      totalCount: filterResult.totalCount,
      unsubscribeUrl,
      viewRecommendationsUrl
    });

    // Envoyer l'email
    const result = await resend.emails.send({
      from: EMAIL_CONFIG.from,
      to: user.email,
      subject: subject,
      html: emailHtml,
      text: textContent,
      replyTo: EMAIL_CONFIG.replyTo
    });

    if (result.error) {
      if (process.env.NODE_ENV === 'development') {
      console.error('❌ Erreur lors de l\'envoi:', result.error);
      }
      throw new Error(`Erreur Resend: ${result.error.message}`);
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Email de recommandations envoyé avec succès (ID: ${result.data?.id})`);
    }

    return {
      success: true,
      messageId: result.data?.id
    };

  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
    console.error('❌ Erreur lors de l\'envoi d\'email de recommandations:', error);
    }
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    };
  }
}

/**
 * Filtre les recommandations pour les emails selon les critères :
 * - Élimine les doublons (même artiste + album)
 * - Ne propose que des albums non possédés si Discogs est connecté
 * - Ne propose que des albums avec liens d'achat
 */
async function filterRecommendationsForEmail(
  recommendations: EmailRecommendation[],
  userId: string
): Promise<{ recommendations: EmailRecommendation[]; totalCount: number }> {
  // Vérifier si l'utilisateur a un compte Discogs connecté
  const hasDiscogs = await db.query.accounts.findFirst({
    where: and(
      eq(accounts.userId, userId),
      eq(accounts.provider, "discogs")
    ),
  });

  let filteredRecommendations = [...recommendations];

  // 1. Éliminer les doublons (même artiste + album)
  const seen = new Set<string>();
  filteredRecommendations = filteredRecommendations.filter(rec => {
    const key = `${rec.artistName.toLowerCase()}|${rec.albumTitle.toLowerCase()}`;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });

  // 2. Si Discogs est connecté, ne proposer que des albums non possédés
  if (hasDiscogs) {
    filteredRecommendations = filteredRecommendations.filter(rec => !rec.isOwned);
  }

  // 3. Ne proposer que des albums avec liens d'achat
  filteredRecommendations = filteredRecommendations.filter(rec =>
    rec.affiliateLinks &&
    Array.isArray(rec.affiliateLinks) &&
    rec.affiliateLinks.length > 0
  );

  // 4. Limiter l'affichage à 10 recommandations maximum pour l'email (lisibilité)
  const displayRecommendations = filteredRecommendations.slice(0, 10);

  if (process.env.NODE_ENV === 'development') {
  console.log(`📧 Filtrage email: ${recommendations.length} → ${filteredRecommendations.length} total → ${displayRecommendations.length} affichées (Discogs: ${hasDiscogs ? 'connecté' : 'non connecté'})`);
  }

  return {
    recommendations: displayRecommendations,
    totalCount: filteredRecommendations.length
  };
}

/**
 * Envoie un email de test (pour le développement)
 */
export async function sendTestEmail(
  toEmail: string
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🧪 Envoi d'email de test à ${toEmail}`);
    }
    }
    }

    if (!process.env.RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not configured');
    }

    const result = await resend.emails.send({
      from: EMAIL_CONFIG.from,
      to: toEmail,
      replyTo: EMAIL_CONFIG.replyTo,
      subject: '🧪 Test Email - Stream2Spin Notifications',
      html: `
        <h1>Test Email</h1>
        <p>Ceci est un email de test pour vérifier la configuration Resend.</p>
        <p>Si vous recevez cet email, la configuration fonctionne correctement !</p>
        <hr>
        <p><small>Stream2Spin - Notifications System</small></p>
      `,
      tags: [
        { name: 'type', value: 'test' }
      ]
    });

    if (result.error) {
      console.error('❌ Erreur Resend (test):', result.error);
      return { success: false, error: result.error.message };
    }

    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Email de test envoyé avec succès (ID: ${result.data?.id})`);
    }
    }
    }
    return { success: true, messageId: result.data?.id };

  } catch (error) {
    console.error('❌ Erreur lors de l\'envoi d\'email de test:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Vérifie la configuration Resend
 */
export function checkResendConfiguration(): { isValid: boolean; message: string } {
  const apiKey = process.env.RESEND_API_KEY;
  const fromEmail = process.env.RESEND_FROM_EMAIL;
  
  if (!apiKey) {
    return {
      isValid: false,
      message: 'RESEND_API_KEY n\'est pas configuré dans les variables d\'environnement'
    };
  }
  
  if (!fromEmail) {
    return {
      isValid: false,
      message: 'RESEND_FROM_EMAIL n\'est pas configuré dans les variables d\'environnement'
    };
  }
  
  if (!apiKey.startsWith('re_')) {
    return {
      isValid: false,
      message: 'La clé API Resend doit commencer par "re_"'
    };
  }
  
  return {
    isValid: true,
    message: `Configuration Resend valide avec l'email: ${fromEmail}`
  };
}

/**
 * Obtient les statistiques d'envoi d'emails (si disponible)
 */
export async function getEmailStats(): Promise<any> {
  try {
    // Note: Resend ne fournit pas d'API de statistiques dans la version gratuite
    // Cette fonction est préparée pour une future implémentation
    if (process.env.NODE_ENV === 'development') {
    console.log('📊 Récupération des statistiques d\'emails...');
    }
    
    return {
      message: 'Statistiques non disponibles avec le plan Resend actuel',
      suggestion: 'Utilisez le dashboard Resend pour voir les statistiques'
    };
    
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des statistiques:', error);
    return null;
  }
}

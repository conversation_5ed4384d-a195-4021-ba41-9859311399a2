/**
 * Amazon Product Advertising API (PAAPI) 5.0 Client
 * 
 * Ce module fournit une interface sécurisée pour interagir avec l'API Product Advertising d'Amazon.
 * Il gère l'authentification AWS Signature v4, la recherche de produits et le parsing des réponses.
 * 
 * Documentation officielle: https://webservices.amazon.com/paapi5/documentation/
 */

import { createHash, createHmac } from 'crypto';

// Configuration de l'API Amazon PAAPI 5.0
const AMAZON_CONFIG = {
  // Endpoint pour la région Europe (France)
  endpoint: 'webservices.amazon.fr',
  region: 'eu-west-1',
  service: 'ProductAdvertisingAPI',
  // Version de l'API
  version: 'paapi5-76308d9c-amazon-com',
  // Opération de recherche
  operation: 'SearchItems'
} as const;

// Interface pour les réponses de l'API Amazon
interface AmazonSearchResponse {
  SearchResult?: {
    Items?: AmazonItem[];
    TotalResultCount?: number;
  };
  Errors?: Array<{
    Code: string;
    Message: string;
  }>;
}

interface AmazonItem {
  ASIN: string;
  DetailPageURL: string;
  Images?: {
    Primary?: {
      Large?: { URL: string };
    };
  };
  ItemInfo?: {
    Title?: { DisplayValue: string };
  };
  Offers?: {
    Listings?: Array<{
      Price?: {
        Amount: number;
        Currency: string;
        DisplayAmount: string;
      };
      Availability?: {
        Message: string;
      };
    }>;
  };
}

// Interface pour les offres retournées par notre client
export interface AmazonOffer {
  vendor: string;
  url: string;
  price: number;
  currency: string;
  merchantId: string;
  productName: string;
  inStock: boolean;
  asin: string;
}

/**
 * Client pour l'API Product Advertising d'Amazon
 */
class AmazonPaapiClient {
  private accessKey: string;
  private secretKey: string;
  private partnerTag: string;

  constructor() {
    // Charger les credentials depuis les variables d'environnement
    this.accessKey = process.env.AMAZON_ACCESS_KEY!;
    this.secretKey = process.env.AMAZON_SECRET_KEY!;
    this.partnerTag = process.env.AMAZON_AFFILIATE_TAG!;

    if (!this.accessKey || !this.secretKey || !this.partnerTag) {
      throw new Error('Amazon PAAPI credentials are missing. Please check AMAZON_ACCESS_KEY, AMAZON_SECRET_KEY, and AMAZON_AFFILIATE_TAG environment variables.');
    }
  }

  /**
   * Recherche des produits vinyles sur Amazon
   */
  async searchVinylProducts(artistName: string, albumTitle: string): Promise<AmazonOffer | null> {
    try {
      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      console.log(`🔍 Recherche Amazon PAAPI pour: "${artistName}" - "${albumTitle}"`);
      }
      }
      }

      // Construire la requête de recherche
      const keyword = this.buildSearchKeyword(artistName, albumTitle);
      
      // Préparer le payload de la requête
      const requestPayload = {
        Keywords: keyword,
        Resources: [
          'Images.Primary.Large',
          'ItemInfo.Title',
          'Offers.Listings.Price',
          'Offers.Listings.Availability'
        ],
        SearchIndex: 'Music',
        ItemCount: 5,
        PartnerTag: this.partnerTag,
        PartnerType: 'Associates',
        Marketplace: 'www.amazon.fr'
      };

      // Créer la requête signée
      const signedRequest = await this.createSignedRequest(requestPayload);
      
      // Exécuter la requête
      const response = await fetch(`https://${AMAZON_CONFIG.endpoint}/paapi5/searchitems`, {
        method: 'POST',
        headers: signedRequest.headers,
        body: JSON.stringify(requestPayload),
        // Timeout de 10 secondes
        signal: AbortSignal.timeout(10000)
      });

      if (!response.ok) {
        throw new Error(`Amazon PAAPI HTTP ${response.status}: ${response.statusText}`);
      }

      const data: AmazonSearchResponse = await response.json();

      // Vérifier les erreurs dans la réponse
      if (data.Errors && data.Errors.length > 0) {
        console.error('Amazon PAAPI errors:', data.Errors);
        return null;
      }

      // Extraire le premier produit valide
      const items = data.SearchResult?.Items;
      if (!items || items.length === 0) {
        if (process.env.NODE_ENV === 'development') {
        console.log(`❌ Aucun produit trouvé sur Amazon pour "${keyword}"`);
        }
        return null;
      }

      // Prendre le premier item et vérifier qu'il a un prix
      const item = items[0];
      const listing = item.Offers?.Listings?.[0];
      
      if (!listing?.Price) {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        console.log(`⚠️ Produit trouvé mais sans prix disponible`);
        }
        }
        }
        return null;
      }

      // Construire l'offre
      const offer: AmazonOffer = {
        vendor: 'Amazon',
        url: item.DetailPageURL,
        price: listing.Price.Amount,
        currency: listing.Price.Currency,
        merchantId: 'amazon-fr',
        productName: item.ItemInfo?.Title?.DisplayValue || `${artistName} - ${albumTitle}`,
        inStock: listing.Availability?.Message !== 'Currently unavailable',
        asin: item.ASIN
      };

      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Offre Amazon trouvée: ${offer.price} ${offer.currency}`);
      }
      }
      }
      return offer;

    } catch (error) {
      console.error('Erreur lors de la recherche Amazon PAAPI:', error);
      return null;
    }
  }

  /**
   * Construit le mot-clé de recherche optimisé pour les vinyles
   */
  private buildSearchKeyword(artistName: string, albumTitle: string): string {
    // Nettoyer et optimiser la recherche
    const cleanArtist = artistName.replace(/[^\w\s]/g, '').trim();
    const cleanAlbum = albumTitle.replace(/[^\w\s]/g, '').trim();
    
    // Ajouter "vinyl" pour cibler spécifiquement les vinyles
    return `${cleanArtist} ${cleanAlbum} vinyl`;
  }

  /**
   * Crée une requête signée avec AWS Signature v4
   */
  private async createSignedRequest(payload: any): Promise<{ headers: Record<string, string> }> {
    const now = new Date();
    const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
    const dateStamp = amzDate.substr(0, 8);

    // Headers requis
    const headers: Record<string, string> = {
      'Content-Type': 'application/json; charset=utf-8',
      'Host': AMAZON_CONFIG.endpoint,
      'X-Amz-Date': amzDate,
      'X-Amz-Target': `com.amazon.paapi5.v1.ProductAdvertisingAPIv1.${AMAZON_CONFIG.operation}`
    };

    // Payload en JSON
    const payloadString = JSON.stringify(payload);
    const payloadHash = createHash('sha256').update(payloadString).digest('hex');

    // Créer la requête canonique
    const canonicalHeaders = Object.keys(headers)
      .sort()
      .map(key => `${key.toLowerCase()}:${headers[key]}`)
      .join('\n');

    const signedHeaders = Object.keys(headers)
      .sort()
      .map(key => key.toLowerCase())
      .join(';');

    const canonicalRequest = [
      'POST',
      '/paapi5/searchitems',
      '',
      canonicalHeaders,
      '',
      signedHeaders,
      payloadHash
    ].join('\n');

    // Créer la chaîne à signer
    const algorithm = 'AWS4-HMAC-SHA256';
    const credentialScope = `${dateStamp}/${AMAZON_CONFIG.region}/${AMAZON_CONFIG.service}/aws4_request`;
    const stringToSign = [
      algorithm,
      amzDate,
      credentialScope,
      createHash('sha256').update(canonicalRequest).digest('hex')
    ].join('\n');

    // Calculer la signature
    const signature = this.calculateSignature(stringToSign, dateStamp);

    // Ajouter l'en-tête d'autorisation
    headers['Authorization'] = `${algorithm} Credential=${this.accessKey}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

    return { headers };
  }

  /**
   * Calcule la signature AWS v4
   */
  private calculateSignature(stringToSign: string, dateStamp: string): string {
    const kDate = createHmac('sha256', `AWS4${this.secretKey}`).update(dateStamp).digest();
    const kRegion = createHmac('sha256', kDate).update(AMAZON_CONFIG.region).digest();
    const kService = createHmac('sha256', kRegion).update(AMAZON_CONFIG.service).digest();
    const kSigning = createHmac('sha256', kService).update('aws4_request').digest();
    
    return createHmac('sha256', kSigning).update(stringToSign).digest('hex');
  }
}

// Instance singleton du client Amazon
let amazonClient: AmazonPaapiClient | null = null;

/**
 * Obtient l'instance du client Amazon PAAPI
 */
export function getAmazonClient(): AmazonPaapiClient {
  if (!amazonClient) {
    amazonClient = new AmazonPaapiClient();
  }
  return amazonClient;
}

/**
 * Fonction utilitaire pour rechercher des produits Amazon
 * Utilisée par le cron job de génération de recommandations
 */
export async function searchAmazonProducts(keyword: string): Promise<AmazonOffer | null> {
  // Extraire l'artiste et l'album du keyword
  const parts = keyword.replace(' vinyl', '').split(' ');
  const midPoint = Math.floor(parts.length / 2);
  const artistName = parts.slice(0, midPoint).join(' ');
  const albumTitle = parts.slice(midPoint).join(' ');

  const client = getAmazonClient();
  return client.searchVinylProducts(artistName, albumTitle);
}
